module.exports = {
  apps: [
    {
      name: "admin",
      script: "dist/index.js",
      env: {
        NODE_ENV: "production",
        APP_ENV: "prod"
      },
      instances: 1,
      exec_mode: "fork",
      max_memory_restart: "1G",
      wait_ready: true,
      kill_timeout: 30000,
      listen_timeout: 15000,
      restart_delay: 5000,
    },
    {
      name: "worker",
      script: "dist/index-worker.js",
      env: {
        NODE_ENV: "production",
        APP_ENV: "prod"
      },
      instances: 1,
      exec_mode: "fork",
      max_memory_restart: "1G",
      wait_ready: true,
      kill_timeout: 30000,
      restart_delay: 5000,
    }
  ]
};
