# reCAPTCHA Enterprise Configuration
# Add these environment variables to your .env file

# Google Cloud Project Configuration
RECAPTCHA_ENTERPRISE_PROJECT_ID=vendure-460019
RECAPTCHA_ENTERPRISE_SITE_KEY=6LdVrVgrAAAAAFzKm0fOR3U5CslmCRcm2fFYsri7

# You'll need to get the secret key from Google Cloud Console
# Go to: https://console.cloud.google.com/security/recaptcha
# RECAPTCHA_ENTERPRISE_SECRET_KEY=your_secret_key_here

# Optional: Fallback to standard reCAPTCHA v3
# RECAPTCHA_V3_SECRET_KEY=your_v3_secret_key_here

# reCAPTCHA Settings
RECAPTCHA_MIN_SCORE=0.5
RECAPTCHA_TIMEOUT=5000

# Google Cloud Authentication
# Option 1: Use service account key file
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Option 2: Use service account key JSON directly
# GOOGLE_APPLICATION_CREDENTIALS_JSON='{"type": "service_account", ...}'

# Development mode (bypasses reCAPTCHA in development)
NODE_ENV=development
