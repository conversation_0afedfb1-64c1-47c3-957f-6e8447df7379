2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Countries' ('en')
2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Zones' ('en')
2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Global settings' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'System' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Expand entries' ('en')
2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Job queue' ('en')
2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'System status' ('en')
2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Scheduled tasks' ('en')
2
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Dashboard' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'No alerts' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Profile' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Log out' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Select display language' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Theme' ('en')
20
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: '{count, plural, one {1 day} other {{count} days}} ago' ('en')
zone.js:2105 Refused to connect to 'https://login-image.vendure.io/?languageCode=en' because it violates the following Content Security Policy directive: "connect-src 'self'".
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Hi! Welcome back. Good to see you.' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Log in to {brand}' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Username' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Password' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Remember me' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Log in' ('en')
ngx-translate-messag…at-compiler.mjs:114 [ngx-translate-messageformat-compiler] Falling back to original invalid message: 'Could not connect to the Vendure server at { url }' ('en')
core.mjs:6550 ERROR 
Hm
handleError	@	core.mjs:6550
﻿

