{"name": "backend", "version": "0.1.0", "license": "UNLICENSED", "private": true, "scripts": {"dev:server": "ts-node ./src/index.ts", "dev:worker": "ts-node ./src/index-worker.ts", "dev": "concurrently pnpm:dev:*", "build": "tsc", "start:server": "node ./dist/index.js", "start:worker": "node ./dist/index-worker.js", "start": "concurrently pnpm:start:*"}, "dependencies": {"@google-cloud/recaptcha-enterprise": "^6.2.0", "@nestjs/common": "^11.1.3", "@pinelab/vendure-plugin-order-export": "^1.4.0", "@types/bcrypt": "^5.0.2", "@vendure/admin-ui-plugin": "^3.3.5", "@vendure/asset-server-plugin": "^3.3.5", "@vendure/core": "^3.3.5", "@vendure/email-plugin": "^3.3.5", "@vendure/graphiql-plugin": "^3.3.5", "@vendure/harden-plugin": "^3.3.5", "axios": "^1.10.0", "bcrypt": "^6.0.0", "body-parser": "^2.2.0", "express-rate-limit": "7.5.1", "googleapis": "150.0.1", "helmet": "^8.1.0", "ioredis": "5.6.1", "joi": "^17.13.3", "node-cron": "^4.2.0", "node-fetch": "^2.6.7", "sendpulse-api": "^1.1.7", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/graphql": "^13.1.0", "@types/express": "^5.0.3", "@types/node": "^24.0.10", "@types/node-cron": "^3.0.11", "@types/pg": "^8.15.4", "@vendure/cli": "minor", "@vendure/mcp-server": "1.0.4-alpha", "@vendure/ui-devkit": "^3.3.5", "chokidar": "^4.0.3", "concurrently": "^9.2.0", "dotenv": "17.0.1", "graphql-tag": "^2.12.6", "pg": "8.16.3", "terser": "^5.43.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}