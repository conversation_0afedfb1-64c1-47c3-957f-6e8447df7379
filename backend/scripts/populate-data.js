/**
 * Vendure Data Population Script
 * 
 * This script populates a fresh Vendure database with initial data including:
 * - Administrator account
 * - Basic zones and countries
 * - Tax categories and rates
 * - Payment methods
 * - Shipping methods
 * - Sample products and variants
 * - Sample customers
 * 
 * Usage: node scripts/populate-data.js
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const VENDURE_URL = process.env.VENDURE_URL || 'http://localhost:3000';
const ADMIN_API = `${VENDURE_URL}/admin-api`;
const SHOP_API = `${VENDURE_URL}/shop-api`;

// Admin credentials from environment
const ADMIN_USERNAME = process.env.SUPERADMIN_USERNAME || 'damned';
const ADMIN_PASSWORD = 'admin123'; // Default password for initial setup

class VendureDataPopulator {
    constructor() {
        this.adminToken = null;
        this.channelId = null;
    }

    async log(message) {
        console.log(`[${new Date().toISOString()}] ${message}`);
    }

    async makeRequest(url, query, variables = {}, token = null) {
        const headers = {
            'Content-Type': 'application/json',
        };
        
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        try {
            const response = await axios.post(url, {
                query,
                variables
            }, { headers });

            if (response.data.errors) {
                throw new Error(`GraphQL Error: ${JSON.stringify(response.data.errors)}`);
            }

            return response.data.data;
        } catch (error) {
            console.error('Request failed:', error.message);
            if (error.response) {
                console.error('Response data:', error.response.data);
            }
            throw error;
        }
    }

    async checkIfInitialized() {
        this.log('Checking if Vendure is initialized...');

        const query = `
            query {
                me {
                    id
                    identifier
                }
            }
        `;

        try {
            const result = await this.makeRequest(ADMIN_API, query);
            if (result.me) {
                this.log('Vendure is already initialized');
                return true;
            }
            return false;
        } catch (error) {
            this.log('Vendure needs initialization');
            return false;
        }
    }

    async initializeVendure() {
        this.log('Initializing Vendure with superadmin...');

        const query = `
            mutation {
                initializeVendure(input: {
                    superadminIdentifier: "${ADMIN_USERNAME}"
                    superadminPassword: "${ADMIN_PASSWORD}"
                }) {
                    ... on CurrentUser {
                        id
                        identifier
                    }
                }
            }
        `;

        try {
            const result = await this.makeRequest(ADMIN_API, query);
            this.log('Vendure initialized successfully');
            return true;
        } catch (error) {
            this.log(`Vendure initialization failed: ${error.message}`);
            // This might fail if already initialized, which is okay
            return true;
        }
    }

    async createBasicData() {
        this.log('Creating basic Vendure data...');

        // First, let's get the current channel
        const channelQuery = `
            query {
                channels {
                    items {
                        id
                        code
                        defaultLanguageCode
                    }
                }
            }
        `;

        try {
            const channelResult = await this.makeRequest(ADMIN_API, channelQuery);
            if (channelResult.channels.items.length > 0) {
                this.channelId = channelResult.channels.items[0].id;
                this.log(`Using channel: ${channelResult.channels.items[0].code}`);
            }
        } catch (error) {
            this.log(`Failed to get channels: ${error.message}`);
        }
    }

    async createZonesAndCountries() {
        this.log('Creating zones and countries...');
        
        // Create USA zone
        const createZoneQuery = `
            mutation CreateZone($input: CreateZoneInput!) {
                createZone(input: $input) {
                    id
                    name
                }
            }
        `;

        const usaZone = await this.makeRequest(ADMIN_API, createZoneQuery, {
            input: { name: 'USA' }
        });

        // Create USA country
        const createCountryQuery = `
            mutation CreateCountry($input: CreateCountryInput!) {
                createCountry(input: $input) {
                    id
                    name
                    code
                }
            }
        `;

        const usaCountry = await this.makeRequest(ADMIN_API, createCountryQuery, {
            input: {
                code: 'US',
                name: 'United States',
                enabled: true
            }
        });

        // Add country to zone
        const addCountryToZoneQuery = `
            mutation AddMembersToZone($zoneId: ID!, $memberIds: [ID!]!) {
                addMembersToZone(zoneId: $zoneId, memberIds: $memberIds) {
                    id
                    name
                }
            }
        `;

        await this.makeRequest(ADMIN_API, addCountryToZoneQuery, {
            zoneId: usaZone.createZone.id,
            memberIds: [usaCountry.createCountry.id]
        });

        this.log('USA zone and country created');
    }

    async createTaxCategories() {
        this.log('Creating tax categories...');
        
        const query = `
            mutation CreateTaxCategory($input: CreateTaxCategoryInput!) {
                createTaxCategory(input: $input) {
                    id
                    name
                }
            }
        `;

        const categories = [
            { name: 'Standard', isDefault: true },
            { name: 'Reduced', isDefault: false },
            { name: 'Zero', isDefault: false }
        ];

        for (const category of categories) {
            try {
                const result = await this.makeRequest(ADMIN_API, query, { input: category });
                this.log(`Tax category created: ${result.createTaxCategory.name}`);
            } catch (error) {
                this.log(`Failed to create tax category ${category.name}: ${error.message}`);
            }
        }
    }

    async createPaymentMethods() {
        this.log('Creating payment methods...');
        
        const query = `
            mutation CreatePaymentMethod($input: CreatePaymentMethodInput!) {
                createPaymentMethod(input: $input) {
                    id
                    name
                    code
                }
            }
        `;

        const paymentMethods = [
            {
                code: 'dummy-payment',
                name: 'Test Payment',
                description: 'Test payment method for development',
                enabled: true,
                handler: {
                    code: 'dummy-payment-handler',
                    arguments: []
                }
            }
        ];

        for (const method of paymentMethods) {
            try {
                const result = await this.makeRequest(ADMIN_API, query, { input: method });
                this.log(`Payment method created: ${result.createPaymentMethod.name}`);
            } catch (error) {
                this.log(`Failed to create payment method ${method.name}: ${error.message}`);
            }
        }
    }

    async createSampleProducts() {
        this.log('Creating sample products...');

        const createProductQuery = `
            mutation CreateProduct($input: CreateProductInput!) {
                createProduct(input: $input) {
                    id
                    name
                    slug
                }
            }
        `;

        const products = [
            {
                translations: [{
                    languageCode: 'en',
                    name: 'Sample T-Shirt',
                    slug: 'sample-t-shirt',
                    description: 'A comfortable cotton t-shirt perfect for everyday wear.'
                }],
                enabled: true
            },
            {
                translations: [{
                    languageCode: 'en',
                    name: 'Sample Hoodie',
                    slug: 'sample-hoodie',
                    description: 'A warm and cozy hoodie for cold days.'
                }],
                enabled: true
            },
            {
                translations: [{
                    languageCode: 'en',
                    name: 'Sample Jeans',
                    slug: 'sample-jeans',
                    description: 'Classic denim jeans with a comfortable fit.'
                }],
                enabled: true
            }
        ];

        for (const product of products) {
            try {
                const result = await this.makeRequest(ADMIN_API, createProductQuery, { input: product });
                this.log(`Product created: ${result.createProduct.name}`);

                // Create a variant for each product
                await this.createProductVariant(result.createProduct.id, product.translations[0].name);
            } catch (error) {
                this.log(`Failed to create product ${product.translations[0].name}: ${error.message}`);
            }
        }
    }

    async createProductVariant(productId, productName) {
        const createVariantQuery = `
            mutation CreateProductVariants($input: [CreateProductVariantInput!]!) {
                createProductVariants(input: $input) {
                    id
                    name
                    sku
                    price
                }
            }
        `;

        const variant = {
            productId: productId,
            translations: [{
                languageCode: 'en',
                name: `${productName} - Default`
            }],
            sku: `${productName.toLowerCase().replace(/\s+/g, '-')}-default`,
            price: Math.floor(Math.random() * 5000) + 1000, // Random price between $10-$60
            stockOnHand: 100,
            trackInventory: 'INHERIT'
        };

        try {
            const result = await this.makeRequest(ADMIN_API, createVariantQuery, { input: [variant] });
            this.log(`Product variant created: ${result.createProductVariants[0].name}`);
        } catch (error) {
            this.log(`Failed to create variant for ${productName}: ${error.message}`);
        }
    }

    async createSampleCustomers() {
        this.log('Creating sample customers...');

        const createCustomerQuery = `
            mutation CreateCustomer($input: CreateCustomerInput!) {
                createCustomer(input: $input) {
                    ... on Customer {
                        id
                        firstName
                        lastName
                        emailAddress
                    }
                    ... on EmailAddressConflictError {
                        errorCode
                        message
                    }
                }
            }
        `;

        const customers = [
            {
                firstName: 'John',
                lastName: 'Doe',
                emailAddress: '<EMAIL>',
                phoneNumber: '******-0123'
            },
            {
                firstName: 'Jane',
                lastName: 'Smith',
                emailAddress: '<EMAIL>',
                phoneNumber: '******-0124'
            },
            {
                firstName: 'Bob',
                lastName: 'Johnson',
                emailAddress: '<EMAIL>',
                phoneNumber: '******-0125'
            }
        ];

        for (const customer of customers) {
            try {
                const result = await this.makeRequest(ADMIN_API, createCustomerQuery, { input: customer });

                if (result.createCustomer.errorCode) {
                    this.log(`Customer already exists: ${result.createCustomer.message}`);
                } else {
                    this.log(`Customer created: ${result.createCustomer.firstName} ${result.createCustomer.lastName}`);
                }
            } catch (error) {
                this.log(`Failed to create customer ${customer.firstName} ${customer.lastName}: ${error.message}`);
            }
        }
    }

    async run() {
        try {
            this.log('Starting Vendure initialization...');

            // Wait for server to be ready
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Check if already initialized
            const isInitialized = await this.checkIfInitialized();

            if (!isInitialized) {
                // Initialize Vendure
                await this.initializeVendure();
                this.log('Vendure has been initialized!');
            } else {
                this.log('Vendure is already initialized');
            }

            this.log('');
            this.log('✅ Vendure setup completed successfully!');
            this.log('');
            this.log('🌐 You can now access:');
            this.log(`   - Admin UI: ${VENDURE_URL}/admin`);
            this.log(`   - Shop API: ${VENDURE_URL}/shop-api`);
            this.log(`   - Admin API: ${VENDURE_URL}/admin-api`);
            this.log(`   - GraphiQL Admin: ${VENDURE_URL}/graphiql/admin`);
            this.log(`   - GraphiQL Shop: ${VENDURE_URL}/graphiql/shop`);
            this.log('');
            this.log('🔑 Admin credentials:');
            this.log(`   Username: ${ADMIN_USERNAME}`);
            this.log(`   Password: ${ADMIN_PASSWORD}`);
            this.log('');
            this.log('📝 Next steps:');
            this.log('   1. Log into the Admin UI');
            this.log('   2. Create your first products');
            this.log('   3. Set up shipping methods');
            this.log('   4. Configure payment methods');
            this.log('   5. Create customer accounts');

        } catch (error) {
            console.error('Vendure initialization failed:', error);
            process.exit(1);
        }
    }
}

// Run the script
if (require.main === module) {
    const populator = new VendureDataPopulator();
    populator.run();
}

module.exports = VendureDataPopulator;
