#!/usr/bin/env node

/**
 * reCAPTCHA v3 Standard API Test
 * Tests the reCAPTCHA v3 integration with standard Google API
 */

// Load environment variables
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

const axios = require('axios');

class RecaptchaV3Tester {
  constructor() {
    this.siteKey = process.env.RECAPTCHA_V3_SITE_KEY;
    this.secretKey = process.env.RECAPTCHA_V3_SECRET_KEY;
    this.results = [];
  }

  async runTests() {
    console.log('🔒 reCAPTCHA v3 Standard API Test\n');
    
    await this.testConfiguration();
    await this.testStandardAPIWithInvalidToken();
    await this.testBackendIntegration();
    
    this.printResults();
    this.printNextSteps();
  }

  async testConfiguration() {
    console.log('🔧 Testing Configuration...');
    
    const hasSiteKey = !!this.siteKey;
    const hasSecretKey = !!this.secretKey;
    
    this.addResult('Site Key Configuration', hasSiteKey, 
      hasSiteKey ? `Site key: ${this.siteKey}` : 'Site key missing');
      
    this.addResult('Secret Key Configuration', hasSecretKey, 
      hasSecretKey ? `Secret key: ${this.secretKey.substring(0, 10)}...` : 'Secret key missing');
  }

  async testStandardAPIWithInvalidToken() {
    console.log('🌐 Testing Google reCAPTCHA API...');
    
    if (!this.secretKey) {
      this.addResult('Google API Test', false, 'Secret key not configured');
      return;
    }

    try {
      // Test with invalid token (should fail gracefully)
      const response = await axios.post(
        'https://www.google.com/recaptcha/api/siteverify',
        new URLSearchParams({
          secret: this.secretKey,
          response: 'invalid-test-token-12345'
        }),
        {
          timeout: 5000,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const data = response.data;
      
      // Should return success: false for invalid token
      const correctlyRejectsInvalidToken = !data.success;
      
      this.addResult('Google API Connection', true, 
        `API responds correctly, success: ${data.success}`);
        
      this.addResult('Invalid Token Rejection', correctlyRejectsInvalidToken, 
        correctlyRejectsInvalidToken ? 'Correctly rejects invalid tokens' : 'Unexpectedly accepts invalid tokens');

    } catch (error) {
      this.addResult('Google API Connection', false, 
        `Error connecting to Google API: ${error.message}`);
    }
  }

  async testBackendIntegration() {
    console.log('🔧 Testing Backend Integration...');
    
    try {
      // Import our verification utility
      const { verifyRecaptchaToken } = require('../src/utils/recaptcha-verification');
      
      // Test with invalid token
      const result = await verifyRecaptchaToken('invalid-test-token', {
        enterprise: false,
        secretKey: this.secretKey,
        expectedAction: 'TEST',
        minScore: 0.5
      });

      // Should return failure for invalid token
      const correctlyRejectsInvalid = !result.success;
      
      this.addResult('Backend Verification Utility', correctlyRejectsInvalid, 
        correctlyRejectsInvalid ? 'Utility correctly rejects invalid tokens' : 'Utility incorrectly accepts invalid tokens');

    } catch (error) {
      this.addResult('Backend Verification Utility', false, 
        `Error testing verification utility: ${error.message}`);
    }
  }

  addResult(testName, passed, message) {
    this.results.push({
      name: testName,
      passed,
      message
    });

    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${Math.round((passed / total) * 100)}%\n`);
  }

  printNextSteps() {
    console.log('🚀 Next Steps for reCAPTCHA Integration:');
    console.log('=======================================\n');

    console.log('1. 🎨 Frontend Integration:');
    console.log('   Add reCAPTCHA to your forms with this script tag:');
    console.log(`   <script src="https://www.google.com/recaptcha/api.js?render=${this.siteKey}"></script>\n`);

    console.log('2. 📝 Generate Tokens in Frontend:');
    console.log(`   grecaptcha.ready(() => {`);
    console.log(`     grecaptcha.execute('${this.siteKey}', {action: 'submit'}).then(token => {`);
    console.log(`       // Send token to backend for verification`);
    console.log(`       console.log('reCAPTCHA token:', token);`);
    console.log(`     });`);
    console.log(`   });\n`);

    console.log('3. 🔒 Backend Verification:');
    console.log('   Your backend is already configured to verify tokens automatically');
    console.log('   Tokens can be sent in headers (x-recaptcha-token) or request body\n');

    console.log('4. 🧪 Testing with Real Tokens:');
    console.log('   - Open your frontend in a browser');
    console.log('   - Open developer tools → Network tab');
    console.log('   - Submit a form with reCAPTCHA');
    console.log('   - Copy the token from the network request');
    console.log('   - Test the token manually with your backend\n');

    console.log('5. 📋 Example Frontend Code:');
    console.log(`
// In your Qwik component:
import { useRecaptchaV3 } from '~/hooks/useRecaptchaV3';

export const SecureForm = component$(() => {
  const { execute } = useRecaptchaV3({
    siteKey: '${this.siteKey}',
    enterprise: false
  });

  const handleSubmit = $(async () => {
    try {
      const token = await execute('form_submit');
      
      const response = await fetch('/shop-api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-recaptcha-token': token
        },
        body: JSON.stringify({
          query: 'mutation { ... }',
          variables: { ... }
        })
      });
      
    } catch (error) {
      console.error('reCAPTCHA error:', error);
    }
  });

  return <form onSubmit$={handleSubmit}>...</form>;
});
`);

    const allPassed = this.results.every(r => r.passed);
    
    if (allPassed) {
      console.log('✅ reCAPTCHA v3 is ready for integration!');
      console.log('   All backend components are working correctly.\n');
    } else {
      console.log('⚠️  Some components need attention before full integration.');
      console.log('   Check the failed tests above and resolve them first.\n');
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new RecaptchaV3Tester();
  tester.runTests().catch(console.error);
}

module.exports = RecaptchaV3Tester;
