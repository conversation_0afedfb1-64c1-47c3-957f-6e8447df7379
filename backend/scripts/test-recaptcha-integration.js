#!/usr/bin/env node
/**
 * Complete reCAPTCHA v3 Integration Test
 * Tests the full reCAPTCHA v3 integration with real credentials
 */

require('dotenv').config();
const axios = require('axios');

console.log('🔒 reCAPTCHA v3 Complete Integration Test\n');

// Configuration from environment
const SITE_KEY = process.env.RECAPTCHA_V3_SITE_KEY;
const SECRET_KEY = process.env.RECAPTCHA_V3_SECRET_KEY;
const MIN_SCORE = parseFloat(process.env.RECAPTCHA_MIN_SCORE) || 0.5;

async function testRecaptchaV3Integration() {
  const results = {
    total: 0,
    passed: 0,
    failed: 0
  };

  function test(name, assertion) {
    results.total++;
    try {
      if (assertion) {
        console.log(`✅ ${name}`);
        results.passed++;
        return true;
      } else {
        console.log(`❌ ${name}`);
        results.failed++;
        return false;
      }
    } catch (error) {
      console.log(`❌ ${name}: ${error.message}`);
      results.failed++;
      return false;
    }
  }

  // Test 1: Environment Configuration
  console.log('🔧 Testing Environment Configuration...');
  test('Site Key Configuration', SITE_KEY && SITE_KEY.length > 0 && SITE_KEY !== 'YOUR_SITE_KEY_HERE');
  test('Secret Key Configuration', SECRET_KEY && SECRET_KEY.length > 0 && SECRET_KEY !== 'YOUR_SECRET_KEY_HERE');
  test('Min Score Configuration', MIN_SCORE >= 0 && MIN_SCORE <= 1);

  // Test 2: Google reCAPTCHA API Connectivity
  console.log('\n🌐 Testing Google reCAPTCHA API...');
  try {
    const response = await axios.post('https://www.google.com/recaptcha/api/siteverify', null, {
      params: {
        secret: SECRET_KEY,
        response: 'invalid_token_for_testing'
      },
      timeout: 5000
    });

    test('Google API Connection', response.status === 200);
    test('Invalid Token Rejection', response.data.success === false);
    
    if (response.data['error-codes']) {
      console.log(`   Error codes: ${response.data['error-codes'].join(', ')}`);
    }
  } catch (error) {
    test('Google API Connection', false);
    console.log(`   API Error: ${error.message}`);
  }

  // Test 3: Test backend verification utility (using require with correct path)
  console.log('\n🔧 Testing Backend Verification Module...');
  try {
    // Import the TypeScript module using require
    const path = require('path');
    const tsPath = path.resolve(__dirname, '../src/utils/recaptcha-verification.ts');
    
    // Check if the file exists
    const fs = require('fs');
    const moduleExists = fs.existsSync(tsPath);
    test('Verification Module Exists', moduleExists);
    
    if (moduleExists) {
      const moduleContent = fs.readFileSync(tsPath, 'utf8');
      test('Module Has verifyRecaptcha Function', moduleContent.includes('verifyRecaptcha'));
      test('Module Supports Standard API', moduleContent.includes('STANDARD_API_URL') || moduleContent.includes('google.com/recaptcha/api/siteverify'));
      test('Module Has Error Handling', moduleContent.includes('catch') || moduleContent.includes('error'));
    }
  } catch (error) {
    test('Backend Integration Test', false);
    console.log(`   Module Error: ${error.message}`);
  }

  // Test 4: Frontend Configuration
  console.log('\n🎨 Testing Frontend Configuration...');
  try {
    const path = require('path');
    const fs = require('fs');
    const frontendEnvPath = path.resolve(__dirname, '../../frontend/.env');
    
    if (fs.existsSync(frontendEnvPath)) {
      const frontendEnv = fs.readFileSync(frontendEnvPath, 'utf8');
      test('Frontend Environment File Exists', true);
      test('Frontend Has Site Key', frontendEnv.includes('PUBLIC_RECAPTCHA_V3_SITE_KEY') && frontendEnv.includes(SITE_KEY));
    } else {
      test('Frontend Environment File Exists', false);
    }
  } catch (error) {
    test('Frontend Configuration Check', false);
    console.log(`   Frontend Error: ${error.message}`);
  }

  // Results Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  console.log(`Total Tests: ${results.total}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);

  // Next Steps
  console.log('\n🚀 reCAPTCHA v3 Integration Status:');
  console.log('===================================');
  
  if (results.failed === 0) {
    console.log('✅ All tests passed! Your reCAPTCHA v3 integration is ready.');
    console.log('\n📋 Ready for Production:');
    console.log('• Backend verification utility configured');
    console.log('• Environment variables set correctly');
    console.log('• Google API connectivity verified');
    console.log('• Frontend configuration ready');
  } else {
    console.log(`⚠️  ${results.failed} test(s) failed. Please address the issues above.`);
  }

  console.log('\n🔗 Next Steps:');
  console.log('• Integrate reCAPTCHA tokens in frontend forms');
  console.log('• Test with real user interactions');
  console.log('• Enable security middleware in production');
  console.log('• Monitor security logs for verification events');

  console.log('\n📝 Example Frontend Integration:');
  console.log(`
// Add to your HTML head:
<script src="https://www.google.com/recaptcha/api.js?render=${SITE_KEY}"></script>

// In your Qwik component:
const handleSubmit = $(async () => {
  const token = await window.grecaptcha.execute('${SITE_KEY}', {action: 'submit'});
  
  // Send token with your request
  await fetch('/shop-api', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-recaptcha-token': token
    },
    body: JSON.stringify(your_graphql_request)
  });
});`);

  return results;
}

// Run the test
testRecaptchaV3Integration()
  .then(results => {
    process.exit(results.failed > 0 ? 1 : 0);
  })
  .catch(error => {
    console.error('❌ Test failed with error:', error.message);
    process.exit(1);
  });
