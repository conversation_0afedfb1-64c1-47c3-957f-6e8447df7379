#!/bin/bash

# Create logs directory if it doesn't exist
mkdir -p /home/<USER>/damneddesigns/backend/logs

# Set appropriate permissions (adjust user:group as needed)
chown -R vendure:vendure /home/<USER>/damneddesigns/backend/logs
chmod 750 /home/<USER>/damneddesigns/backend/logs

# Create logrotate configuration
cat > /etc/logrotate.d/vendure-logs << 'EOL'
/home/<USER>/damneddesigns/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0640 vendure vendure
    sharedscripts
    postrotate
        # Send USR1 signal to Node.js process to reopen log files
        [ -f /home/<USER>/damneddesigns/backend/pids/app.pid ] && kill -USR1 $(cat /home/<USER>/damneddesigns/backend/pids/app.pid) || true
    endscript
}
EOL

echo "Logging setup complete. Logs will be stored in /home/<USER>/damneddesigns/backend/logs/"
echo "Log rotation has been configured in /etc/logrotate.d/vendure-logs"
