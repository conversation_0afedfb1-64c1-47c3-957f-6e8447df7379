/**
 * Reset Admin Password Script
 * 
 * This script resets the admin password for the Vendure superadmin user.
 * 
 * Usage: node scripts/reset-admin-password.js [new-password]
 */

const bcrypt = require('bcrypt');
const { Client } = require('pg');

// Database configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'vendure',
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'darkweb@123',
};

const ADMIN_USERNAME = process.env.SUPERADMIN_USERNAME || 'damned';
const NEW_PASSWORD = process.argv[2] || 'admin123';

async function resetAdminPassword() {
    const client = new Client(dbConfig);
    
    try {
        console.log('Connecting to database...');
        await client.connect();
        
        // Check if user exists
        console.log(`Checking if user '${ADMIN_USERNAME}' exists...`);
        const userQuery = `
            SELECT u.id, u.identifier, a.id as admin_id 
            FROM "user" u 
            LEFT JOIN administrator a ON u.id = a."userId" 
            WHERE u.identifier = $1
        `;
        
        const userResult = await client.query(userQuery, [ADMIN_USERNAME]);
        
        if (userResult.rows.length === 0) {
            console.log(`❌ User '${ADMIN_USERNAME}' not found!`);
            console.log('Creating new superadmin user...');
            
            // Create new user and administrator
            await createSuperAdmin(client);
        } else {
            console.log(`✅ User '${ADMIN_USERNAME}' found`);
            const user = userResult.rows[0];
            
            if (!user.admin_id) {
                console.log('❌ User exists but is not an administrator!');
                console.log('Converting user to administrator...');
                await makeUserAdmin(client, user.id);
            }
            
            // Update password
            await updatePassword(client, user.id);
        }
        
        console.log('');
        console.log('✅ Admin password reset completed!');
        console.log('');
        console.log('🔑 Admin credentials:');
        console.log(`   Username: ${ADMIN_USERNAME}`);
        console.log(`   Password: ${NEW_PASSWORD}`);
        console.log('');
        console.log('🌐 Admin UI: http://localhost:3000/admin');
        
    } catch (error) {
        console.error('❌ Error resetting admin password:', error);
        process.exit(1);
    } finally {
        await client.end();
    }
}

async function createSuperAdmin(client) {
    console.log('Creating new superadmin user...');
    
    // Hash the password
    const passwordHash = await bcrypt.hash(NEW_PASSWORD, 12);
    
    // Start transaction
    await client.query('BEGIN');
    
    try {
        // Create user
        const userInsert = `
            INSERT INTO "user" (identifier, verified, "createdAt", "updatedAt")
            VALUES ($1, true, NOW(), NOW())
            RETURNING id
        `;
        const userResult = await client.query(userInsert, [ADMIN_USERNAME]);
        const userId = userResult.rows[0].id;
        
        // Create authentication method
        const authInsert = `
            INSERT INTO authentication_method ("userId", identifier, "passwordHash", strategy, "createdAt", "updatedAt", type)
            VALUES ($1, $2, $3, 'native', NOW(), NOW(), 'native')
        `;
        await client.query(authInsert, [userId, ADMIN_USERNAME, passwordHash]);
        
        // Create administrator
        const adminInsert = `
            INSERT INTO administrator ("userId", "firstName", "lastName", "emailAddress", "createdAt", "updatedAt")
            VALUES ($1, 'Super', 'Admin', $2, NOW(), NOW())
            RETURNING id
        `;
        await client.query(adminInsert, [userId, ADMIN_USERNAME]);
        
        // Get SuperAdmin role ID
        const roleQuery = `SELECT id FROM role WHERE code = 'superadmin'`;
        const roleResult = await client.query(roleQuery);
        
        if (roleResult.rows.length > 0) {
            const roleId = roleResult.rows[0].id;
            
            // Assign SuperAdmin role
            const userRoleInsert = `
                INSERT INTO user_roles_role ("userId", "roleId")
                VALUES ($1, $2)
            `;
            await client.query(userRoleInsert, [userId, roleId]);
        }
        
        await client.query('COMMIT');
        console.log('✅ Superadmin user created successfully');
        
    } catch (error) {
        await client.query('ROLLBACK');
        throw error;
    }
}

async function makeUserAdmin(client, userId) {
    // Create administrator record
    const adminInsert = `
        INSERT INTO administrator ("userId", "firstName", "lastName", "emailAddress", "createdAt", "updatedAt")
        VALUES ($1, 'Super', 'Admin', $2, NOW(), NOW())
    `;
    await client.query(adminInsert, [userId, ADMIN_USERNAME]);
    
    // Get SuperAdmin role ID and assign it
    const roleQuery = `SELECT id FROM role WHERE code = 'superadmin'`;
    const roleResult = await client.query(roleQuery);
    
    if (roleResult.rows.length > 0) {
        const roleId = roleResult.rows[0].id;
        
        // Check if user already has this role
        const existingRoleQuery = `
            SELECT 1 FROM user_roles_role 
            WHERE "userId" = $1 AND "roleId" = $2
        `;
        const existingRole = await client.query(existingRoleQuery, [userId, roleId]);
        
        if (existingRole.rows.length === 0) {
            const userRoleInsert = `
                INSERT INTO user_roles_role ("userId", "roleId")
                VALUES ($1, $2)
            `;
            await client.query(userRoleInsert, [userId, roleId]);
        }
    }
    
    console.log('✅ User converted to administrator');
}

async function updatePassword(client, userId) {
    console.log('Updating password...');

    // Hash the new password
    const passwordHash = await bcrypt.hash(NEW_PASSWORD, 12);

    // First, delete all existing authentication methods for this user
    console.log('Cleaning up existing authentication methods...');
    const deleteQuery = `DELETE FROM authentication_method WHERE "userId" = $1`;
    await client.query(deleteQuery, [userId]);

    // Create a single, clean authentication method
    const insertQuery = `
        INSERT INTO authentication_method ("userId", identifier, "passwordHash", strategy, "createdAt", "updatedAt", type)
        VALUES ($1, $2, $3, 'native', NOW(), NOW(), 'NativeAuthenticationMethod')
    `;
    await client.query(insertQuery, [userId, ADMIN_USERNAME, passwordHash]);

    console.log('✅ Password updated successfully');
}

// Run the script
if (require.main === module) {
    resetAdminPassword();
}

module.exports = { resetAdminPassword };
