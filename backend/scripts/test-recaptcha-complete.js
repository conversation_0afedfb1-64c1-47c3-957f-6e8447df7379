#!/usr/bin/env node

/**
 * Complete reCAPTCHA Integration Test
 * 
 * This script demonstrates the full reCAPTCHA Enterprise integration flow
 * and provides instructions for testing with real tokens.
 */

// Load environment variables first
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

// Register ts-node to handle TypeScript imports
require('ts-node/register');

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = process.env.VENDURE_URL || 'http://localhost:3000';
const SHOP_API = `${BASE_URL}/shop-api`;

class RecaptchaIntegrationTester {
  constructor() {
    this.results = [];
  }

  async runTests() {
    console.log('🔒 reCAPTCHA Enterprise Integration Test\n');
    
    // Test 1: Verify backend can handle reCAPTCHA tokens
    await this.testBackendRecaptchaSupport();
    
    // Test 2: Check environment configuration
    await this.testEnvironmentConfiguration();
    
    // Test 3: Test with real frontend integration
    await this.testFrontendIntegration();
    
    // Test 4: Validate reCAPTCHA verification utility
    await this.testRecaptchaVerificationUtility();
    
    this.printResults();
    this.printIntegrationInstructions();
  }

  async testBackendRecaptchaSupport() {
    console.log('🔧 Testing Backend reCAPTCHA Support...');
    
    try {
      // Test GraphQL mutation that would normally require reCAPTCHA
      const mutation = {
        query: `
          mutation authenticate($username: String!, $password: String!) {
            authenticate(input: { username: $username, password: $password }) {
              ... on CurrentUser {
                id
                identifier
              }
              ... on ErrorResult {
                errorCode
                message
              }
            }
          }
        `,
        variables: {
          username: '<EMAIL>',
          password: 'testpassword'
        }
      };

      // Test without reCAPTCHA token
      const responseWithoutToken = await axios.post(SHOP_API, mutation, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'RecaptchaTest/1.0'
        }
      });

      // Test with fake reCAPTCHA token in header
      const responseWithFakeToken = await axios.post(SHOP_API, mutation, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'RecaptchaTest/1.0',
          'x-recaptcha-token': 'fake-test-token-12345'
        }
      });

      // Test with reCAPTCHA token in body
      const mutationWithToken = {
        ...mutation,
        variables: {
          ...mutation.variables,
          recaptchaToken: 'fake-test-token-67890'
        }
      };

      const responseWithBodyToken = await axios.post(SHOP_API, mutationWithToken, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'RecaptchaTest/1.0'
        }
      });

      this.addResult('Backend reCAPTCHA Support', true, 
        'Backend accepts reCAPTCHA tokens in headers and body');

    } catch (error) {
      this.addResult('Backend reCAPTCHA Support', false, 
        `Error: ${error.message}`);
    }
  }

  async testEnvironmentConfiguration() {
    console.log('🌍 Testing Environment Configuration...');
    
    const requiredEnvVars = [
      'RECAPTCHA_ENTERPRISE_PROJECT_ID',
      'RECAPTCHA_ENTERPRISE_SITE_KEY',
      'GOOGLE_APPLICATION_CREDENTIALS'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length === 0) {
      this.addResult('Environment Configuration', true, 
        'All required environment variables are set');
    } else {
      this.addResult('Environment Configuration', false, 
        `Missing environment variables: ${missingVars.join(', ')}`);
    }

    // Check if service account file exists
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credentialsPath && fs.existsSync(credentialsPath)) {
      this.addResult('Service Account File', true, 
        `Service account file exists at ${credentialsPath}`);
    } else {
      this.addResult('Service Account File', false, 
        'Google Cloud service account file not found');
    }
  }

  async testFrontendIntegration() {
    console.log('🎨 Testing Frontend Integration...');
    
    // Check if frontend hook exists
    const hookPath = path.join(__dirname, '../../frontend/src/hooks/useRecaptchaV3.ts');
    if (fs.existsSync(hookPath)) {
      this.addResult('Frontend Hook', true, 
        'useRecaptchaV3 hook exists');
    } else {
      this.addResult('Frontend Hook', false, 
        'useRecaptchaV3 hook not found');
    }

    // Check if reCAPTCHA components exist
    const componentsPath = path.join(__dirname, '../../frontend/src/components/security');
    if (fs.existsSync(componentsPath)) {
      const files = fs.readdirSync(componentsPath);
      const hasProvider = files.includes('RecaptchaProvider.tsx');
      const hasExamples = files.includes('SecureFormExamples.tsx');
      
      this.addResult('Frontend Components', hasProvider && hasExamples, 
        `Security components found: Provider=${hasProvider}, Examples=${hasExamples}`);
    } else {
      this.addResult('Frontend Components', false, 
        'Security components directory not found');
    }
  }

  async testRecaptchaVerificationUtility() {
    console.log('🔐 Testing reCAPTCHA Verification Utility...');
    
    try {
      // Import the verification utility
      const { verifyRecaptchaToken } = require('../src/utils/recaptcha-verification');
      
      // Test with invalid token (should fail gracefully)
      const result = await verifyRecaptchaToken('invalid-token', {
        enterprise: true,
        projectId: process.env.RECAPTCHA_ENTERPRISE_PROJECT_ID || 'test-project',
        siteKey: process.env.RECAPTCHA_ENTERPRISE_SITE_KEY || 'test-key',
        expectedAction: 'TEST',
        minScore: 0.5
      });

      // Should return failure for invalid token
      const isWorking = !result.success && result.error_codes;
      
      this.addResult('reCAPTCHA Verification Utility', isWorking, 
        `Utility correctly rejects invalid tokens: ${JSON.stringify(result)}`);

    } catch (error) {
      this.addResult('reCAPTCHA Verification Utility', false, 
        `Error importing or using utility: ${error.message}`);
    }
  }

  addResult(testName, passed, message) {
    this.results.push({
      name: testName,
      passed,
      message
    });

    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${Math.round((passed / total) * 100)}%\n`);
  }

  printIntegrationInstructions() {
    console.log('🚀 reCAPTCHA Integration Instructions:');
    console.log('=====================================\n');

    console.log('1. 🔑 Complete Google Cloud Setup:');
    console.log('   - Set RECAPTCHA_ENTERPRISE_PROJECT_ID in .env');
    console.log('   - Set RECAPTCHA_ENTERPRISE_SITE_KEY in .env');
    console.log('   - Set GOOGLE_APPLICATION_CREDENTIALS path in .env');
    console.log('   - Ensure service account has reCAPTCHA Enterprise permissions\n');

    console.log('2. 🎨 Frontend Integration:');
    console.log('   - Add PUBLIC_RECAPTCHA_V3_SITE_KEY to frontend .env');
    console.log('   - Import and use RecaptchaProvider in root layout');
    console.log('   - Use useRecaptchaV3 hook in forms that need protection');
    console.log('   - Call executeRecaptcha("ACTION_NAME") before form submission\n');

    console.log('3. 🧪 Testing with Real Tokens:');
    console.log('   - Start frontend development server');
    console.log('   - Open browser developer tools');
    console.log('   - Use a form with reCAPTCHA integration');
    console.log('   - Monitor network requests for reCAPTCHA tokens');
    console.log('   - Copy token and test backend verification manually\n');

    console.log('4. 📋 Example Integration Code:');
    console.log(`
// Frontend form submission:
const handleSubmit = async () => {
  const token = await executeRecaptcha('CHECKOUT');
  const response = await fetch('/shop-api', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-recaptcha-token': token
    },
    body: JSON.stringify({
      query: 'mutation { ... }',
      variables: { ... }
    })
  });
};

// Backend will automatically verify the token via security middleware
`);

    console.log('5. 🔍 Monitoring:');
    console.log('   - Check security logs in backend/logs/security/');
    console.log('   - Monitor reCAPTCHA scores and actions');
    console.log('   - Set up alerts for low scores or failed verifications\n');

    const hasAllEnv = process.env.RECAPTCHA_ENTERPRISE_PROJECT_ID && 
                     process.env.RECAPTCHA_ENTERPRISE_SITE_KEY && 
                     process.env.GOOGLE_APPLICATION_CREDENTIALS;

    if (!hasAllEnv) {
      console.log('⚠️  WARNING: Environment variables not fully configured.');
      console.log('   Complete Google Cloud setup to enable full reCAPTCHA functionality.\n');
    } else {
      console.log('✅ Environment configuration looks good!');
      console.log('   Ready for reCAPTCHA Enterprise integration.\n');
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new RecaptchaIntegrationTester();
  tester.runTests().catch(console.error);
}

module.exports = RecaptchaIntegrationTester;
