#!/usr/bin/env node

/**
 * Standard reCAPTCHA v3 Integration Test
 * Tests the standard Google reCAPTCHA v3 API (not Enterprise)
 */

// Load environment variables
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

const axios = require('axios');

class StandardRecaptchaTest {
  constructor() {
    this.results = [];
  }

  async runTests() {
    console.log('🔒 Standard reCAPTCHA v3 Integration Test\n');
    
    await this.testEnvironmentConfiguration();
    await this.testStandardAPI();
    await this.testBackendIntegration();
    
    this.printResults();
    this.printNextSteps();
  }

  async testEnvironmentConfiguration() {
    console.log('🌍 Testing Environment Configuration...');
    
    const siteKey = process.env.RECAPTCHA_V3_SITE_KEY;
    const secretKey = process.env.RECAPTCHA_V3_SECRET_KEY;
    
    if (siteKey && siteKey !== 'YOUR_SECRET_KEY_HERE') {
      this.addResult('Site Key Configuration', true, 
        `Site key configured: ${siteKey.substring(0, 20)}...`);
    } else {
      this.addResult('Site Key Configuration', false, 
        'Site key not configured');
    }

    if (secretKey && secretKey !== 'YOUR_SECRET_KEY_HERE') {
      this.addResult('Secret Key Configuration', true, 
        `Secret key configured: ${secretKey.substring(0, 20)}...`);
    } else {
      this.addResult('Secret Key Configuration', false, 
        'Secret key not configured - this is needed from Google reCAPTCHA console');
    }
  }

  async testStandardAPI() {
    console.log('🔧 Testing Standard reCAPTCHA API...');
    
    const secretKey = process.env.RECAPTCHA_V3_SECRET_KEY;
    
    if (!secretKey || secretKey === 'YOUR_SECRET_KEY_HERE') {
      this.addResult('reCAPTCHA API Test', false, 
        'Cannot test API without secret key');
      return;
    }

    try {
      // Test with invalid token (should fail)
      const response = await axios.post(
        'https://www.google.com/recaptcha/api/siteverify',
        new URLSearchParams({
          secret: secretKey,
          response: 'invalid-test-token'
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      const result = response.data;
      
      // Should return success: false for invalid token
      if (result.success === false) {
        this.addResult('reCAPTCHA API Test', true, 
          'API correctly rejects invalid tokens');
      } else {
        this.addResult('reCAPTCHA API Test', false, 
          'API response unexpected');
      }

    } catch (error) {
      this.addResult('reCAPTCHA API Test', false, 
        `API error: ${error.message}`);
    }
  }

  async testBackendIntegration() {
    console.log('🔗 Testing Backend Integration...');
    
    try {
      // Import our verification utility
      const { verifyRecaptchaToken } = require('../src/utils/recaptcha-verification');
      
      // Test with invalid token
      const result = await verifyRecaptchaToken('invalid-test-token', {
        enterprise: false,
        secretKey: process.env.RECAPTCHA_V3_SECRET_KEY,
        expectedAction: 'TEST',
        minScore: 0.5
      });

      // Should return failure for invalid token
      if (!result.success) {
        this.addResult('Backend Integration', true, 
          'Backend correctly handles reCAPTCHA verification');
      } else {
        this.addResult('Backend Integration', false, 
          'Backend verification unexpected result');
      }

    } catch (error) {
      this.addResult('Backend Integration', false, 
        `Backend error: ${error.message}`);
    }
  }

  addResult(testName, passed, message) {
    this.results.push({
      name: testName,
      passed,
      message
    });

    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${Math.round((passed / total) * 100)}%\n`);
  }

  printNextSteps() {
    console.log('🚀 Next Steps:');
    console.log('==============\n');

    const secretKey = process.env.RECAPTCHA_V3_SECRET_KEY;
    
    if (!secretKey || secretKey === 'YOUR_SECRET_KEY_HERE') {
      console.log('⚠️  ACTION REQUIRED: Get your reCAPTCHA secret key');
      console.log('1. Go to https://www.google.com/recaptcha/admin');
      console.log('2. Find your site key: 6LdVrVgrAAAAAFzKm0fOR3U5CslmCRcm2fFYsri7');
      console.log('3. Click on it to view details');
      console.log('4. Copy the SECRET KEY (not the site key)');
      console.log('5. Update your .env file:');
      console.log('   RECAPTCHA_V3_SECRET_KEY=your-secret-key-here\n');
    } else {
      console.log('✅ Configuration looks good!');
      console.log('\n📋 Frontend Integration:');
      console.log('Add to your frontend .env:');
      console.log(`PUBLIC_RECAPTCHA_V3_SITE_KEY=${process.env.RECAPTCHA_V3_SITE_KEY}\n`);
      
      console.log('🎯 Frontend Usage Example:');
      console.log(`
// In your Qwik component:
import { useRecaptchaV3 } from '~/hooks/useRecaptchaV3';

export const MyForm = component$(() => {
  const { execute } = useRecaptchaV3({
    siteKey: '${process.env.RECAPTCHA_V3_SITE_KEY}',
    enterprise: false
  });

  const handleSubmit = $(async () => {
    const token = await execute('submit');
    // Send token with your form data
    await fetch('/shop-api', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-recaptcha-token': token
      },
      body: JSON.stringify({
        query: 'mutation { ... }',
        variables: { ... }
      })
    });
  });

  return <form onSubmit$={handleSubmit}>...</form>;
});
`);
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new StandardRecaptchaTest();
  tester.runTests().catch(console.error);
}

module.exports = StandardRecaptchaTest;
