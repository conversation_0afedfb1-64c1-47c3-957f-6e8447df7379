{"compilerOptions": {"module": "nodenext", "moduleResolution": "nodenext", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "target": "ES2017", "lib": ["es2017", "esnext.asynciterable"], "strict": true, "sourceMap": false, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./"}, "include": ["src/**/*"], "exclude": ["node_modules", "migration.ts", "src/plugins/**/ui/*", "src/plugins/**/dashboard/*", "admin-ui", "admin-ui-build", "vite.*.*ts", "tests/**/*", "dist/**/*"], "ts-node": {"files": true}}