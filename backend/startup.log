yarn run v1.22.22
warning ../package.json: No license field
$ node ./dist/index.js
verbose 5/23/25, 12:55 AM - [HardenPlugin] Configuring HideValidationErrorsPlugin 
verbose 5/23/25, 12:55 AM - [HardenPlugin] Configuring HideValidationErrorsPlugin 
info 5/23/25, 12:55 AM - [Vendure Server] Bootstrapping Vendure Server (pid: 184649)... 
info 5/23/25, 12:55 AM - [Vendure Server] The plugin "NmiPaymentPlugin" does not specify a compatibility range, so it is not guaranteed to be compatible with this version of Vendure. 
error Command failed with signal "SIGTERM".
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
