yarn run v1.22.22
warning ../package.json: No license field
$ node ./dist/index.js
info 5/23/25, 1:16 AM - [Vendure Server] Bootstrapping Vendure Server (pid: 191850)... 
info 5/23/25, 1:16 AM - [Vendure Server] The plugin "NmiPaymentPlugin" does not specify a compatibility range, so it is not guaranteed to be compatible with this version of Vendure. 
info 5/23/25, 1:16 AM - [AdminUiPlugin] Creating admin ui middleware (prod mode) 
info 5/23/25, 1:16 AM - [AssetServerPlugin] Creating asset server middleware 
info 5/23/25, 1:16 AM - [RoutesResolver] HealthController {/health}: 
info 5/23/25, 1:16 AM - [RouterExplorer] Mapped {/health, GET} route 
info 5/23/25, 1:16 AM - [GraphQLModule] Mapped {/shop-api, POST} route 
info 5/23/25, 1:16 AM - [GraphQLModule] Mapped {/admin-api, POST} route 
info 5/23/25, 1:16 AM - [NestApplication] Nest application successfully started 
error 5/23/25, 1:16 AM - [NestApplication] Error: listen EADDRINUSE: address already in use :::3000 
Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1908:16)
    at listenInCluster (node:net:1965:12)
    at Server.listen (node:net:2067:7)
    at ExpressAdapter.listen (/home/<USER>/damneddesigns/backend/node_modules/@nestjs/platform-express/adapters/express-adapter.js:109:32)
    at /home/<USER>/damneddesigns/backend/node_modules/@nestjs/core/nest-application.js:183:30
    at new Promise (<anonymous>)
    at NestApplication.listen (/home/<USER>/damneddesigns/backend/node_modules/@nestjs/core/nest-application.js:173:16)
    at async bootstrap (/home/<USER>/damneddesigns/backend/node_modules/@vendure/core/dist/bootstrap.js:115:5) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '::',
  port: 3000
}
error Command failed with signal "SIGTERM".
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
