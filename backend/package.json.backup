{"name": "backend", "version": "0.1.0", "private": true, "scripts": {"dev:server": "ts-node ./src/index.ts", "dev:worker": "ts-node ./src/index-worker.ts", "dev": "concurrently npm:dev:*", "build": "tsc", "start:server": "node ./dist/index.js", "start:worker": "node ./dist/index-worker.js", "start": "concurrently npm:start:*"}, "dependencies": {"@vendure/admin-ui-plugin": "3.3.1", "@vendure/asset-server-plugin": "3.3.1", "@vendure/core": "3.3.1", "@vendure/email-plugin": "3.3.1", "@vendure/graphiql-plugin": "3.3.1", "@vendure/harden-plugin": "^3.3.1", "axios": "^1.9.0", "body-parser": "^2.2.0", "dotenv": "16.5.0", "express-rate-limit": "7.5.0", "googleapis": "148.0.0", "ioredis": "5.6.1", "nodemailer-smtp-transport": "2.7.4", "pg": "8.16.0"}, "devDependencies": {"@vendure/cli": "3.3.1", "@vendure/ui-devkit": "3.3.1", "concurrently": "9.1.2", "typescript": "5.8.3"}}