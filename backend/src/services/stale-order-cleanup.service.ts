import { Injectable, OnApplicationBootstrap, OnApplicationShutdown } from '@nestjs/common';
import * as cron from 'node-cron';
import {
    Logger,
    Order,
    OrderService,
    RequestContext,
    ChannelService,
} from '@vendure/core';

const loggerCtx = 'StaleOrderCleanupService';

@Injectable()
export class StaleOrderCleanupService implements OnApplicationBootstrap, OnApplicationShutdown {
    private cleanupJob: cron.ScheduledTask | null = null;
    private readonly CLEANUP_CRON = process.env.ORDER_CLEANUP_CRON || '0 * * * *'; // Every hour by default
    private readonly MAX_AGE_MINUTES = Number(process.env.ORDER_CLEANUP_MAX_AGE_MINUTES || 30);
    private readonly BATCH_SIZE = Number(process.env.ORDER_CLEANUP_BATCH_SIZE || 100);
    private readonly MAX_PAGES = Number(process.env.ORDER_CLEANUP_MAX_PAGES || 100);
    private readonly RUN_ON_STARTUP = process.env.ORDER_CLEANUP_RUN_ON_STARTUP?.toLowerCase() === 'true';

    constructor(
        private orderService: OrderService,
        private channelService: ChannelService,
    ) {}

    async onApplicationBootstrap() {
        // Optionally run cleanup immediately on startup
        if (this.RUN_ON_STARTUP) {
            Logger.info('Running initial cleanup on startup...', loggerCtx);
            await this.runCleanup();
        }
        
        // Start the recurring cleanup job
        await this.startRecurringCleanup();
        
        Logger.info(
            `Stale order cleanup service initialized - cron schedule: ${this.CLEANUP_CRON}, max age: ${this.MAX_AGE_MINUTES} minutes, batch size: ${this.BATCH_SIZE}, max pages: ${this.MAX_PAGES}`,
            loggerCtx,
        );
    }

    onApplicationShutdown() {
        if (this.cleanupJob) {
            this.cleanupJob.stop();
            Logger.info('Stale order cleanup service stopped', loggerCtx);
        }
    }

    private async startRecurringCleanup() {
        // Start cron job for recurring cleanup
        this.cleanupJob = cron.schedule(this.CLEANUP_CRON, async () => {
            await this.runCleanup();
        });
        
        Logger.info(
            `Scheduled stale order cleanup with cron: ${this.CLEANUP_CRON}`,
            loggerCtx,
        );
    }

    private async runCleanup(): Promise<void> {
        Logger.info(
            `Starting scheduled stale order cleanup (maxAge: ${this.MAX_AGE_MINUTES} minutes)`,
            loggerCtx,
        );
        
        try {
            const cancelledCount = await this.cancelStaleOrders(this.MAX_AGE_MINUTES);
            
            if (cancelledCount > 0) {
                Logger.info(
                    `Cleanup completed: cancelled ${cancelledCount} stale orders`,
                    loggerCtx,
                );
            } else {
                Logger.info('No stale orders found to clean up', loggerCtx);
            }
        } catch (error) {
            Logger.error(
                `Cleanup job failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                loggerCtx,
            );
        }
    }

    /**
     * Manually trigger cleanup for immediate use (e.g., for testing or manual cleanup)
     */
    async triggerManualCleanup(maxAgeMinutes?: number): Promise<number> {
        const ageLimit = maxAgeMinutes || this.MAX_AGE_MINUTES;
        
        Logger.info(`Manual cleanup triggered (maxAge: ${ageLimit} minutes)`, loggerCtx);
        
        try {
            const cancelledCount = await this.cancelStaleOrders(ageLimit);
            
            Logger.info(`Manual cleanup completed: cancelled ${cancelledCount} stale orders`, loggerCtx);
            
            return cancelledCount;
        } catch (error) {
            Logger.error(
                `Manual cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                loggerCtx,
            );
            return 0;
        }
    }

    /**
     * Cancel stale orders that are stuck in ArrangingPayment or AddingItems state using batched pagination
     */
    private async cancelStaleOrders(maxAgeMinutes: number): Promise<number> {
        const cutoffTime = new Date(Date.now() - maxAgeMinutes * 60 * 1000);
        
        try {
            // Create admin RequestContext for order operations
            const defaultChannel = await this.channelService.getDefaultChannel();
            const ctx = new RequestContext({
                apiType: 'admin',
                authorizedAsOwnerOnly: false,
                isAuthorized: true,
                channel: defaultChannel,
            });

            let page = 0;
            let totalCancelled = 0;
            let moreOrders = true;

            Logger.debug(`Starting batched order cleanup with cutoff time: ${cutoffTime.toISOString()}`, loggerCtx);

            while (moreOrders) {
                // Use Vendure's OrderService.findAll with proper filters and pagination
                // Look for orders in both ArrangingPayment and AddingItems states
                const { items: staleOrders } = await this.orderService.findAll(ctx, {
                    filter: {
                        state: { in: ['ArrangingPayment', 'AddingItems'] },
                        updatedAt: { before: cutoffTime },
                    },
                    take: this.BATCH_SIZE,
                    skip: page * this.BATCH_SIZE,
                });
                
                moreOrders = staleOrders.length > 0;
                
                if (staleOrders.length === 0) {
                    if (page === 0) {
                        Logger.verbose('No stale orders found to clean up', loggerCtx);
                    }
                    break;
                }
                
                Logger.info(
                    `Processing batch ${page + 1}: Found ${staleOrders.length} stale orders (batch size: ${this.BATCH_SIZE})`,
                    loggerCtx,
                );
                
                let batchCancelled = 0;
                
                for (const order of staleOrders) {
                    try {
                        // Use proper Vendure state transition instead of direct DB update
                        const result = await this.orderService.transitionToState(ctx, order.id, 'Cancelled');
                        
                        if (result instanceof Order) {
                            batchCancelled++;
                            totalCancelled++;
                            Logger.verbose(
                                `Cancelled stale order ${order.code} (ID: ${order.id}, age: ${this.getOrderAge(order.updatedAt)} minutes)`,
                                loggerCtx,
                            );
                        } else {
                            Logger.warn(
                                `Failed to transition stale order ${order.code} to Cancelled state: ${result.message}`,
                                loggerCtx,
                            );
                        }
                    } catch (error) {
                        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                        Logger.error(
                            `Failed to cancel stale order ${order.code}: ${errorMessage}`,
                            loggerCtx,
                        );
                        
                        // Log verbose details for debugging
                        Logger.verbose(
                            `Order ${order.code} cancellation error details: ${JSON.stringify({
                                orderId: order.id,
                                orderCode: order.code,
                                currentState: order.state,
                                updatedAt: order.updatedAt,
                                error: errorMessage,
                            })}`,
                            loggerCtx,
                        );
                    }
                }
                
                Logger.info(
                    `Batch ${page + 1} completed: cancelled ${batchCancelled}/${staleOrders.length} orders`,
                    loggerCtx,
                );
                
                page++;
                
                // Safety limit to prevent runaway pagination
                if (page > this.MAX_PAGES) {
                    Logger.warn(
                        `Reached maximum pagination limit (${this.MAX_PAGES} pages). Stopping cleanup. Total cancelled: ${totalCancelled}`,
                        loggerCtx,
                    );
                    break;
                }
            }
            
            if (totalCancelled > 0) {
                Logger.info(
                    `Cleanup completed: cancelled ${totalCancelled} stale orders across ${page} batches`,
                    loggerCtx,
                );
            }
            
            return totalCancelled;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error(
                `Error during stale order cleanup: ${errorMessage}`,
                loggerCtx,
            );
            
            // Log detailed error information for debugging
            Logger.verbose(
                `Cleanup error details: ${JSON.stringify({
                    maxAgeMinutes,
                    cutoffTime: cutoffTime.toISOString(),
                    error: errorMessage,
                    stack: error instanceof Error ? error.stack : undefined,
                })}`,
                loggerCtx,
            );
            
            throw error;
        }
    }

    /**
     * One-time migration to clean up all AddingItems orders (for pre-local-cart migration cleanup)
     */
    async cleanupAllAddingItemsOrders(): Promise<number> {
        Logger.info('Starting one-time cleanup of all AddingItems orders (pre-migration cleanup)', loggerCtx);
        
        try {
            // Create admin RequestContext for order operations
            const defaultChannel = await this.channelService.getDefaultChannel();
            const ctx = new RequestContext({
                apiType: 'admin',
                authorizedAsOwnerOnly: false,
                isAuthorized: true,
                channel: defaultChannel,
            });

            let page = 0;
            let totalCancelled = 0;
            let moreOrders = true;

            while (moreOrders) {
                // Find ALL orders in AddingItems state (no age filter)
                const { items: addingItemsOrders } = await this.orderService.findAll(ctx, {
                    filter: {
                        state: { eq: 'AddingItems' },
                    },
                    take: this.BATCH_SIZE,
                    skip: page * this.BATCH_SIZE,
                });
                
                moreOrders = addingItemsOrders.length > 0;
                
                if (addingItemsOrders.length === 0) {
                    if (page === 0) {
                        Logger.info('No AddingItems orders found to clean up', loggerCtx);
                    }
                    break;
                }
                
                Logger.info(
                    `Processing migration batch ${page + 1}: Found ${addingItemsOrders.length} AddingItems orders`,
                    loggerCtx,
                );
                
                let batchCancelled = 0;
                
                for (const order of addingItemsOrders) {
                    try {
                        // Use proper Vendure state transition
                        const result = await this.orderService.transitionToState(ctx, order.id, 'Cancelled');
                        
                        if (result instanceof Order) {
                            batchCancelled++;
                            totalCancelled++;
                            Logger.verbose(
                                `Cancelled pre-migration order ${order.code} (ID: ${order.id}, age: ${this.getOrderAge(order.updatedAt)} minutes)`,
                                loggerCtx,
                            );
                        } else {
                            Logger.warn(
                                `Failed to transition pre-migration order ${order.code} to Cancelled state: ${result.message}`,
                                loggerCtx,
                            );
                        }
                    } catch (error) {
                        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                        Logger.error(
                            `Failed to cancel pre-migration order ${order.code}: ${errorMessage}`,
                            loggerCtx,
                        );
                    }
                }
                
                Logger.info(
                    `Migration batch ${page + 1} completed: cancelled ${batchCancelled}/${addingItemsOrders.length} orders`,
                    loggerCtx,
                );
                
                page++;
                
                // Safety limit
                if (page > this.MAX_PAGES) {
                    Logger.warn(
                        `Reached maximum pagination limit (${this.MAX_PAGES} pages) during migration. Stopping cleanup. Total cancelled: ${totalCancelled}`,
                        loggerCtx,
                    );
                    break;
                }
            }
            
            Logger.info(
                `Migration cleanup completed: cancelled ${totalCancelled} pre-migration AddingItems orders across ${page} batches`,
                loggerCtx,
            );
            
            return totalCancelled;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error(
                `Error during migration cleanup: ${errorMessage}`,
                loggerCtx,
            );
            throw error;
        }
    }

    /**
     * Calculate the age of an order in minutes
     */
    private getOrderAge(updatedAt: Date): number {
        return Math.floor((Date.now() - new Date(updatedAt).getTime()) / (1000 * 60));
    }
}
