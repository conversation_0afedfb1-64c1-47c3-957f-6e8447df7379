import {
  Create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  PaymentMethod<PERSON><PERSON><PERSON>,
  RequestContext,
  Payment,
  Order,
  PaymentMetadata,
  CreateRefundResult,
  SettlePaymentResult,
} from '@vendure/core';
import axios, { AxiosInstance } from 'axios';

interface SezzleConfig {
  merchantUuid: string;
  apiKey: string;
  baseUrl: string;
  timeout: number;
}

interface SezzleOrderRequest {
  intent: 'AUTH' | 'CAPTURE';
  purchase_country: string;
  purchase_currency: string;
  order_amount: number;
  tax_amount?: number;
  shipping_amount?: number;
  handling_amount?: number;
  requires_shipping_info?: boolean;
  order_items: Array<{
    name: string;
    sku?: string;
    description?: string;
    quantity: number;
    price: number;
    tax_amount?: number;
    url?: string;
  }>;
  billing_contact: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  shipping_contact?: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  metadata?: Record<string, any>;
  cancel_url: string;
  complete_url: string;
  complete_auto_redirect?: boolean;
  require_shipping_info?: boolean;
  customer_origin_ip?: string;
  customer_origin_user_agent?: string;
}

interface SezzleOrderResponse {
  id: string;
  uuid: string;
  intent: 'AUTH' | 'CAPTURE';
  reference_id: string;
  order_amount: number;
  tax_amount: number;
  shipping_amount: number;
  handling_amount: number;
  status: 'APPROVED' | 'PENDING' | 'FAILED' | 'CANCELLED' | 'REFUNDED';
  checkout_url: string;
  links: Array<{
    rel: string;
    method: 'GET' | 'POST' | 'PATCH' | 'DELETE';
    href: string;
  }>;
  created_at: string;
  updated_at: string;
}

interface SezzleRefundRequest {
  amount: number;
  reason?: string;
  merchant_notes?: string;
  metadata?: Record<string, any>;
}

class SezzleClient {
  private client: AxiosInstance;
  private config: SezzleConfig;

  constructor(config: Partial<SezzleConfig> = {}) {
    this.config = {
      merchantUuid: process.env.SEZZLE_MERCHANT_UUID || '',
      apiKey: process.env.SEZZLE_API_KEY || '',
      baseUrl: process.env.SEZZLE_BASE_URL || 'https://sandbox.gateway.sezzle.com',
      timeout: parseInt(process.env.SEZZLE_TIMEOUT_MS || '10000', 10),
      ...config,
    };

    if (!this.config.merchantUuid || !this.config.apiKey) {
      throw new Error('Sezzle merchant UUID and API key are required');
    }

    this.client = axios.create({
      baseURL: `${this.config.baseUrl.replace(/\/+$/, '')}/v2`, // Ensure no double slashes
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Basic ${Buffer.from(`${this.config.merchantUuid}:${this.config.apiKey}`).toString('base64')}`,
      },
    });
  }

  async createOrder(orderData: SezzleOrderRequest): Promise<SezzleOrderResponse> {
    try {
      const response = await this.client.post<SezzleOrderResponse>('/order', orderData);
      return response.data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message;
      Logger.error(`Sezzle create order failed: ${errorMessage}`, 'SezzlePaymentHandler');
      if (error.response?.data) {
        Logger.error(`Sezzle error details: ${JSON.stringify(error.response.data)}`, 'SezzlePaymentHandler');
      }
      throw new Error(errorMessage || 'Failed to create Sezzle order');
    }
  }

  async getOrder(orderId: string): Promise<SezzleOrderResponse> {
    try {
      const response = await this.client.get<SezzleOrderResponse>(`/order/${orderId}`);
      return response.data;
    } catch (error: any) {
      Logger.error(`Sezzle get order failed: ${error.message}`, 'SezzlePaymentHandler');
      throw new Error('Failed to fetch Sezzle order details');
    }
  }

  async refundOrder(orderId: string, refundData: SezzleRefundRequest): Promise<{ id: string; status: string }> {
    try {
      const response = await this.client.post(`/order/${orderId}/refund`, refundData);
      return response.data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message;
      Logger.error(`Sezzle refund failed: ${errorMessage}`, 'SezzlePaymentHandler');
      if (error.response?.data) {
        Logger.error(`Sezzle refund error details: ${JSON.stringify(error.response.data)}`, 'SezzlePaymentHandler');
      }
      throw new Error(errorMessage || 'Failed to process Sezzle refund');
    }
  }
}

export const sezzlePaymentHandler = new PaymentMethodHandler({
  code: 'sezzle-payment',
  description: [
    { languageCode: LanguageCode.en, value: 'Sezzle - Buy Now, Pay Later' },
  ],
  args: {
    testMode: {
      type: 'boolean',
      label: [{ languageCode: LanguageCode.en, value: 'Test Mode' }],
      description: [{
        languageCode: LanguageCode.en,
        value: 'When enabled, transactions will be processed in test mode',
      }],
      defaultValue: true,
    },
    autoCapture: {
      type: 'boolean',
      label: [{ languageCode: LanguageCode.en, value: 'Auto Capture' }],
      description: [{
        languageCode: LanguageCode.en,
        value: 'When enabled, payments will be automatically captured',
      }],
      defaultValue: true,
    },
    requireShippingInfo: {
      type: 'boolean',
      label: [{ languageCode: LanguageCode.en, value: 'Require Shipping Info' }],
      description: [{
        languageCode: LanguageCode.en,
        value: 'When enabled, shipping information is required before checkout',
      }],
      defaultValue: true,
    },
  },

  createPayment: async (ctx, order, amount, args, metadata): Promise<CreatePaymentResult> => {
    try {
      const sezzle = new SezzleClient({
        baseUrl: args.testMode 
          ? 'https://sandbox.gateway.sezzle.com' 
          : 'https://gateway.sezzle.com',
      });

      const orderItems = order.lines.map(line => ({
        name: line.productVariant.name,
        sku: line.productVariant.sku,
        quantity: line.quantity,
        price: line.proratedUnitPrice / 100, // Convert to dollars
      }));

      const billingAddress = order.billingAddress;
      const shippingAddress = order.shippingAddress || billingAddress;

      const sezzleOrder: SezzleOrderRequest = {
        intent: args.autoCapture ? 'CAPTURE' : 'AUTH',
        purchase_country: billingAddress.countryCode || 'US',
        purchase_currency: order.currencyCode || 'USD',
        order_amount: order.total / 100, // Convert to dollars
        tax_amount: order.taxSummary.reduce((sum, tax) => sum + tax.taxTotal, 0) / 100,
        shipping_amount: order.shipping / 100,
        order_items: orderItems,
        billing_contact: {
          first_name: (billingAddress as any).firstName || (billingAddress.fullName?.split(' ')[0] || 'Customer'),
          last_name: (billingAddress as any).lastName || (billingAddress.fullName?.split(' ').slice(1).join(' ') || 'Name'),
          email: order.customer?.emailAddress || '',
          address1: billingAddress.streetLine1 || '',
          address2: billingAddress.streetLine2 || '',
          city: billingAddress.city || '',
          state: billingAddress.province || '',
          postal_code: billingAddress.postalCode || '',
          country: billingAddress.countryCode || 'US',
        },
        shipping_contact: {
          first_name: (shippingAddress as any).firstName || (shippingAddress.fullName?.split(' ')[0] || ''),
          last_name: (shippingAddress as any).lastName || (shippingAddress.fullName?.split(' ').slice(1).join(' ') || ''),
          email: order.customer?.emailAddress || '',
          address1: shippingAddress.streetLine1 || '',
          address2: shippingAddress.streetLine2 || '',
          city: shippingAddress.city || '',
          state: shippingAddress.province || '',
          postal_code: shippingAddress.postalCode || '',
          country: shippingAddress.countryCode || 'US',
        },
        metadata: {
          orderCode: order.code,
          customerId: order.customer?.id,
        },
        cancel_url: `${process.env.STOREFRONT_URL || 'http://localhost:3000'}/checkout/payment?canceled=true`,
        complete_url: `${process.env.STOREFRONT_URL || 'http://localhost:3000'}/checkout/confirmation/${order.code}`,
        complete_auto_redirect: true,
        require_shipping_info: args.requireShippingInfo,
      };

      const result = await sezzle.createOrder(sezzleOrder);

      return {
        amount: amount,
        state: 'Authorized' as const,
        transactionId: result.id,
        metadata: {
          sezzleOrderId: result.id,
          sezzleOrderUuid: result.uuid,
          checkoutUrl: result.checkout_url,
          status: result.status,
        },
      };
    } catch (error: any) {
      Logger.error(`Sezzle payment error: ${error.message}`, 'SezzlePaymentHandler');
      return {
        amount: amount,
        state: 'Declined' as const,
        metadata: {
          errorMessage: error.message || 'Payment processing failed',
        },
      };
    }
  },

  settlePayment: async (ctx, order, payment, args): Promise<SettlePaymentResult> => {
    // For Sezzle, we consider the payment settled when createPayment succeeds
    return {
      success: true,
      metadata: payment.metadata,
    };
  },

  createRefund: async (
    ctx: RequestContext,
    input: { amount?: number; reason?: string },
    amount: number,
    order: Order,
    payment: Payment,
    args: any,
  ): Promise<CreateRefundResult> => {
    try {
      const sezzle = new SezzleClient({
        baseUrl: args.testMode 
          ? 'https://sandbox.gateway.sezzle.com' 
          : 'https://gateway.sezzle.com',
      });

      const sezzleOrderId = (payment.metadata as PaymentMetadata)?.sezzleOrderId;
      if (!sezzleOrderId) {
        throw new Error('No Sezzle order ID found in payment metadata');
      }

      const refundData: SezzleRefundRequest = {
        amount: (input.amount || amount) / 100, // Convert to dollars
        reason: input.reason || 'Customer requested refund',
        merchant_notes: `Refund for order ${order.code}`,
      };

      const result = await sezzle.refundOrder(sezzleOrderId, refundData);

      return {
        state: 'Settled' as const,
        transactionId: result.id,
        metadata: {
          refundId: result.id,
          status: result.status,
          refundAmount: amount,
          refundReason: input.reason,
        },
      };
    } catch (error: any) {
      Logger.error(`Sezzle refund error: ${error.message}`, 'SezzlePaymentHandler');
      return {
        state: 'Failed' as const,
        metadata: {
          errorMessage: error.message || 'Refund processing failed',
        },
      };
    }
  },
});
