import { VendurePlugin, PluginCommonModule, EventBus, OrderEvent, Logger, RequestContext } from '@vendure/core';
import { Request } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

@VendurePlugin({
  imports: [PluginCommonModule],
})
export class OrderCreationLoggerPlugin {
  private logFilePath: string = path.join('/home/<USER>/damneddesigns', 'order-creation-events.log');
  
  private logToFile(message: string): void {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    fs.appendFile(this.logFilePath, logEntry, (err) => {
      if (err) {
        Logger.error(`Failed to write to order log file: ${err.message}`, 'OrderCreationLogger');
      }
    });
  }
  
  /**
   * Get a formatted stack trace without node_modules
   */
  private getCleanStackTrace(): string {
    const stackObj = {};
    Error.captureStackTrace(stackObj);
    const stack = (stackObj as any).stack || '';
    
    // Filter out stack frames from node_modules
    return stack
      .split('\n')
      .filter((line: string) => !line.includes('node_modules/'))
      .join('\n');
  }
  constructor(eventBus: EventBus) {
    eventBus.ofType(OrderEvent).subscribe(event => {
      if (event.type === 'created') {
        try {
          // Get the request object if available
          const ctx = event.ctx as RequestContext;
          const req = ctx.req as Request;
          
          // Basic order info
          this.logToFile(`===== NEW ORDER CREATED =====`);
          Logger.info(`[OrderCreationLogger] ===== NEW ORDER CREATED =====`, 'OrderCreationLogger');
          this.logToFile(`Order Code: ${event.order.code}`);
          Logger.info(`[OrderCreationLogger] Order Code: ${event.order.code}`, 'OrderCreationLogger');
          this.logToFile(`Order ID: ${event.order.id}`);
          Logger.info(`[OrderCreationLogger] Order ID: ${event.order.id}`, 'OrderCreationLogger');
          this.logToFile(`Customer: ${event.order.customer?.id ?? 'guest'}`);
          Logger.info(`[OrderCreationLogger] Customer: ${event.order.customer?.id ?? 'guest'}`, 'OrderCreationLogger');
          this.logToFile(`Total: ${event.order.totalWithTax}`);
          Logger.info(`[OrderCreationLogger] Total: ${event.order.totalWithTax}`, 'OrderCreationLogger');
          
          // Request details that triggered it
          if (req) {
            // Detailed request information
            this.logToFile(`Request URL: ${req.originalUrl || req.url}`);
            this.logToFile(`Request Method: ${req.method}`);
            this.logToFile(`User Agent: ${req.headers['user-agent']}`);
            this.logToFile(`Referer: ${req.headers.referer || 'none'}`);
            this.logToFile(`IP Address: ${req.ip}`);
            this.logToFile(`X-Forwarded-For: ${req.headers['x-forwarded-for'] || 'none'}`);
            this.logToFile(`Host: ${req.headers.host || 'none'}`);
            this.logToFile(`Origin: ${req.headers.origin || 'none'}`);
            
            // Log the request body
            try {
              let bodyContent = 'Empty body';
              if (req.body) {
                if (typeof req.body === 'string') {
                  bodyContent = req.body.substring(0, 1000); // Limit to 1000 chars
                } else if (typeof req.body === 'object') {
                  bodyContent = JSON.stringify(req.body).substring(0, 1000);
                }
              }
              this.logToFile(`Request Body: ${bodyContent}`);
            } catch (e: any) {
              this.logToFile(`Error logging request body: ${e?.message || 'Unknown error'}`); 
            }
            
            // Log request connection info
            this.logToFile(`Connection Remote Address: ${req.connection?.remoteAddress || 'none'}`);
            this.logToFile(`Connection Remote Port: ${req.connection?.remotePort || 'none'}`);
            
            Logger.info(`[OrderCreationLogger] Request URL: ${req.originalUrl || req.url}`, 'OrderCreationLogger');
            Logger.info(`[OrderCreationLogger] Request Method: ${req.method}`, 'OrderCreationLogger');
            Logger.info(`[OrderCreationLogger] User Agent: ${req.headers['user-agent']}`, 'OrderCreationLogger');
            Logger.info(`[OrderCreationLogger] Referer: ${req.headers.referer || 'none'}`, 'OrderCreationLogger');
            Logger.info(`[OrderCreationLogger] IP Address: ${req.ip}`, 'OrderCreationLogger');
          } else {
            this.logToFile(`No request context available`);
            Logger.info(`[OrderCreationLogger] No request context available`, 'OrderCreationLogger');
          }
          
          // Log environment information
          this.logToFile(`Process ID: ${process.pid}`);
          this.logToFile(`Process Title: ${process.title}`);
          this.logToFile(`Node Version: ${process.version}`);
          this.logToFile(`Server Hostname: ${os.hostname()}`);
          
          // Capture stack trace
          this.logToFile(`Stack Trace: ${this.getCleanStackTrace()}`);
          
          // Order contents
          if (event.order.lines && event.order.lines.length > 0) {
            this.logToFile(`Order contains ${event.order.lines.length} line items:`);
            Logger.info(`[OrderCreationLogger] Order contains ${event.order.lines.length} line items:`, 'OrderCreationLogger');
            
            event.order.lines.forEach((line, index) => {
              const itemInfo = `  Item ${index+1}: ${line.quantity}x ${line.productVariant?.name || 'Unknown'} (${line.productVariant?.id})`;
              this.logToFile(itemInfo);
              Logger.info(`[OrderCreationLogger] ${itemInfo}`, 'OrderCreationLogger');
            });
          } else {
            this.logToFile(`Order contains NO line items (empty order)`);
            Logger.info(`[OrderCreationLogger] Order contains NO line items (empty order)`, 'OrderCreationLogger');
          }
          
          // Stack trace to see what code path triggered this
          const stack = new Error().stack;
          this.logToFile(`Stack trace: ${stack}`);
          Logger.info(`[OrderCreationLogger] Stack trace: ${stack}`, 'OrderCreationLogger');
          
          this.logToFile(`===== END ORDER DETAILS =====`);
          Logger.info(`[OrderCreationLogger] ===== END ORDER DETAILS =====`, 'OrderCreationLogger');
        } catch (error) {
          // Fallback to basic logging if enhanced logging fails
          const basicInfo = `New order created (basic info): code=${event.order.code}, id=${event.order.id}, customerId=${event.order.customer?.id ?? 'guest'}, total=${event.order.totalWithTax}`;
          this.logToFile(basicInfo);
          this.logToFile(`Error during enhanced logging: ${error}`);
          
          Logger.info(`[OrderCreationLogger] ${basicInfo}`, 'OrderCreationLogger');
          Logger.error(`[OrderCreationLogger] Error during enhanced logging: ${error}`, 'OrderCreationLogger');
        }
      }
    });
    Logger.info('[OrderCreationLogger] Plugin initialized and listening for order creation events.', 'OrderCreationLogger');
  }

  static init() {
    return this;
  }
}
