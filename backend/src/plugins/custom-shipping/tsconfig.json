{"compilerOptions": {"target": "es2018", "module": "commonjs", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@vendure/core": ["node_modules/@vendure/core"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}