# reCAPTCHA Enterprise Configuration
# Copy this to your .env file and update with your actual values

# reCAPTCHA Enterprise Settings
RECAPTCHA_ENTERPRISE_PROJECT_ID=your-gcp-project-id
RECAPTCHA_ENTERPRISE_SITE_KEY=6LdVrVgrAAAAAFzKm0fOR3U5CslmCRcm2fFYsri7
RECAPTCHA_SECRET_KEY=your-secret-key-for-fallback

# Google Cloud Authentication
# Option 1: Service Account Key (less secure, not recommended for production)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Option 2: Use Google Cloud SDK authentication (recommended)
# Run: gcloud auth application-default login

# Redis Configuration (for rate limiting)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# CSRF Protection
CSRF_SECRET=your-csrf-secret-key

# Security Logging
LOG_BASE_PATH=/home/<USER>/damneddesigns/backend/logs

# Environment
NODE_ENV=development
APP_ENV=dev
