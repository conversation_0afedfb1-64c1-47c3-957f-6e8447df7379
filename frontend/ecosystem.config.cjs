module.exports = {
  apps: [
    {
      name: 'store',
      script: 'pnpm',
      args: 'serve',
      cwd: '/home/<USER>/damneddesigns/frontend',
      env: {
        NODE_ENV: 'production',
        PORT: 4000,
        HOST: '0.0.0.0',
        // Security enhancements
        NODE_OPTIONS: '--max-old-space-size=1024',
        // Explicitly unset problematic pnpm environment variables
        NPM_CONFIG_VERIFY_DEPS_BEFORE_RUN: undefined,
        NPM_CONFIG__JSR_REGISTRY: undefined,
      },
      // Note: Vite preview server doesn't support cluster mode
      // Using fork mode with enhanced monitoring instead
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      
      // Enhanced Security and Monitoring
      kill_timeout: 5000, // Graceful shutdown timeout
      listen_timeout: 3000, // Startup timeout
      max_restarts: 10, // Limit restart attempts
      min_uptime: '10s', // Minimum uptime before considering stable
      
      // Enhanced Logging with timestamps
      log_file: './logs/pm2-frontend.log',
      out_file: './logs/pm2-frontend-out.log',
      error_file: './logs/pm2-frontend-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Health Monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
    }
  ]
};