import { component$, useSignal, useVisibleTask$ } from '@builder.io/qwik';

export interface OrderProcessingModalProps {
  visible: boolean;
}

const messages = [
  '🔥 Forging your items in hellfire...',
  '🧌 Summoning the payment demons...',
  '📜 Consulting the dark ledgers...',
  '💀 Slaying the credit card demons...',
  '🗡️ Sharpening our transaction blade...',
  '🏦 Battling the bank overlords...',
  '🩸 Making a blood oath...'
];

export const OrderProcessingModal = component$<OrderProcessingModalProps>(({ visible }) => {
  const currentIdx = useSignal(0);
  const showModal = useSignal(visible);
  const intervalRef = useSignal<any>(null);

  useVisibleTask$(({ track, cleanup }) => {
    track(() => visible);

    // Immediately hide modal when visible becomes false
    if (!visible) {
      showModal.value = false;
      currentIdx.value = 0;
      if (intervalRef.value) {
        clearInterval(intervalRef.value);
        intervalRef.value = null;
      }
      return;
    }
    
    if (visible) {
      showModal.value = true;
      currentIdx.value = 0;
      if (intervalRef.value) {
        clearInterval(intervalRef.value);
      }
      intervalRef.value = setInterval(() => {
        if (currentIdx.value < messages.length - 1) {
          currentIdx.value++;
        } else {
          currentIdx.value = 0;
        }
      }, 1500);
    }
    
    cleanup(() => {
      if (intervalRef.value) {
        clearInterval(intervalRef.value);
        intervalRef.value = null;
      }
      showModal.value = false;
      currentIdx.value = 0;
    });
  });

  if (!showModal.value) return null;

  // Loader icon (spinner)
  return (
    <div class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 backdrop-blur-xs">
      <div class="bg-zinc-900 text-white p-8 rounded-2xl shadow-2xl w-[90%] max-w-lg text-center border border-zinc-700 relative overflow-hidden">
        {/* Emergency close button */}
        <button
          onClick$={() => {
            showModal.value = false;
            if (intervalRef.value) {
              clearInterval(intervalRef.value);
              intervalRef.value = null;
            }
          }}
          class="absolute top-4 right-4 text-zinc-400 hover:text-white transition-colors z-20"
          title="Close"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        
        {/* Subtle background pattern */}
        <div class="absolute inset-0 opacity-5">
          <div class="absolute inset-0 bg-linear-to-br from-red-800/20 to-orange-800/20"></div>
        </div>
        <div class="relative z-10">
          {/* Loader spinner */}
          <div class="mb-6 flex items-center justify-center min-h-[96px]">
            <div class="relative mx-auto w-16 h-16">
              <div class="absolute inset-0 rounded-full border-4 border-red-600/30"></div>
              <div class="absolute inset-0 rounded-full border-4 border-transparent border-t-red-500 animate-spin"></div>
              <div class="absolute inset-2 bg-red-600/20 rounded-full animate-pulse"></div>
              <div class="absolute inset-4 bg-red-500/40 rounded-full animate-ping"></div>
            </div>
          </div>
          {/* Message with quick transitions */}
          <div class="mb-4" style={{ minHeight: '2.5em' }}>
            <p class="text-xl font-mono font-medium tracking-wide leading-relaxed transition-all duration-300">
              {messages[currentIdx.value]}
            </p>
          </div>
          {/* Subtle brand messaging */}
          <div class="mt-6 text-xs text-zinc-400 font-mono opacity-75">
            DAMNED DESIGNS • PAYMENT IN PROGRESS
          </div>
        </div>
      </div>
    </div>
  );
});
