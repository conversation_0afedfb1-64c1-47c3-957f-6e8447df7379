import { component$ } from '@qwik.dev/core';
import { useDocumentHead, useLocation } from '@qwik.dev/router';
import { DEFAULT_METADATA_TITLE } from '~/constants';
import { generateDocumentHead } from '~/utils';

export const Head = component$(() => {
	const documentHead = useDocumentHead();
	const head =
		documentHead.meta.length > 0 ? documentHead : { ...documentHead, ...generateDocumentHead() };
	const loc = useLocation();

	return (
		<head>
			<meta charSet="utf-8" />
			<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
			<meta name="theme-color" content="#000000" />
			<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
			
			{/* iPhone Advanced Privacy Protection compatibility */}
			<meta name="apple-mobile-web-app-capable" content="yes" />
			<meta name="apple-mobile-web-app-title" content="Damned Designs" />
			<meta name="format-detection" content="telephone=no" />
			<meta name="msapplication-tap-highlight" content="no" />
			
			{/* Privacy-friendly tracking prevention */}
			<meta name="referrer" content="strict-origin-when-cross-origin" />
			<meta httpEquiv="Content-Security-Policy" content="default-src 'self'; img-src 'self' data: https:; font-src 'self' data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://demo.vendure.io;" />
			
			{/* Disable problematic features that trigger privacy warnings */}
			<meta name="apple-touch-fullscreen" content="yes" />
			<meta name="mobile-web-app-capable" content="yes" />
			
			{/* Additional iOS privacy-friendly settings */}
			<meta name="apple-mobile-web-app-orientations" content="portrait" />
			<meta name="apple-mobile-web-app-status-bar-style" content="default" />
			<meta name="format-detection" content="telephone=no" />
			<meta name="msapplication-TileColor" content="#000000" />
			<meta name="msapplication-config" content="none" />
			
			{/* Preload critical resources to avoid loading delays */}
			<link rel="dns-prefetch" href="https://demo.vendure.io" />
			<link rel="preconnect" href="https://demo.vendure.io" crossOrigin="" />
			
			{/* Service Worker for iOS privacy optimization */}
			<script dangerouslySetInnerHTML={`
				if ('serviceWorker' in navigator && /iPad|iPhone|iPod/.test(navigator.userAgent)) {
					window.addEventListener('load', () => {
						navigator.serviceWorker.register('/ios-privacy-sw.js')
							.catch(() => {
								// Silent fail - service workers are optional
							});
					});
				}
			`} />
			<title>{head.title || DEFAULT_METADATA_TITLE}</title>

			<link rel="manifest" href="/manifest.json" />
			{/* Font preloading to eliminate FOUT (Flash of Unstyled Text) */}
			<link rel="preload" href="/fonts/playfair-display/playfair-display-v37-latin-700.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
			<link rel="preload" href="/fonts/inter/inter-v19-latin-regular.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
			<link rel="preload" href="/fonts/inter/inter-v19-latin-500.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />

			{/* Favicons with light/dark mode support */}
			<link rel="icon" href="/favicon-light.svg" type="image/svg+xml" media="(prefers-color-scheme: light)" />
			<link rel="icon" href="/favicon-dark.svg" type="image/svg+xml" media="(prefers-color-scheme: dark)" />
			<link rel="icon" href="/favicon.svg" type="image/svg+xml" />
			{/* Fallback for browsers that insist on favicon.ico */}
			<link rel="shortcut icon" href="/favicon.svg" />
			<link rel="icon" href="/favicon.svg" sizes="any" />
			
			<link rel="preconnect" href="https://demo.vendure.io" />
			<link rel="canonical" href={loc.url.toString()} />

			{head.meta.map((m, key) => (
				<meta key={key} {...m} />
			))}

			{head.links.map((l, key) => (
				<link key={key} {...l} />
			))}

			{head.styles.map(({ key, style, ...props }) => (
				<style key={key} {...props} dangerouslySetInnerHTML={style} />
			))}

			<meta name="description" content="Damned Designs" />
		</head>
	);
});
