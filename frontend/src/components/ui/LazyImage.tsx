import { component$, useSignal, useVisibleTask$, type QRL, $ } from '@qwik.dev/core';
import { isImageCached, isImageLikelyCached } from '~/utils/image-cache';

// Helper function to generate responsive srcset for Vendure assets
const generateResponsiveSources = (src: string, widths: number[] = [320, 640, 1024, 1280]) => {
  // Check if this is a Vendure asset URL
  const isVendureAsset = src.includes('/assets/') || src.includes('/assetspreview/') || src.includes('damneddesigns.com');
  
  if (!isVendureAsset) {
    // For non-Vendure images, return as-is
    return { 
      avif: null, 
      webp: null, 
      original: src,
      avifSrcset: null,
      webpSrcset: null,
      originalSrcset: null
    };
  }
  
  const baseUrl = src.split('?')[0]; // Remove existing query params
  
  // Map widths to appropriate Vendure presets for better quality
  const getPresetForWidth = (width: number) => {
    if (width <= 160) return 'thumb';
    if (width <= 320) return 'small';
    if (width <= 640) return 'medium';
    if (width <= 1280) return 'large';
    if (width <= 1600) return 'xl';
    if (width <= 2048) return 'xxl';
    if (width <= 2560) return 'ultra';
    return '4k'; // For monitors larger than 2560px
  };
  
  // Generate srcset using presets for optimal quality, with custom width override for fine control
  const avifSrcset = widths.map(w => {
    const preset = getPresetForWidth(w);
    return `${baseUrl}?preset=${preset}&w=${w}&format=avif ${w}w`;
  }).join(', ');
  
  const webpSrcset = widths.map(w => {
    const preset = getPresetForWidth(w);
    return `${baseUrl}?preset=${preset}&w=${w}&format=webp ${w}w`;
  }).join(', ');
  
  const originalSrcset = widths.map(w => {
    const preset = getPresetForWidth(w);
    return `${baseUrl}?preset=${preset}&w=${w} ${w}w`;
  }).join(', ');
  
  // Single format URLs for fallback
  const separator = '?';
  const avifUrl = `${baseUrl}${separator}format=avif`;
  const webpUrl = `${baseUrl}${separator}format=webp`;
  
  return { 
    avif: avifUrl, 
    webp: webpUrl, 
    original: baseUrl,
    avifSrcset,
    webpSrcset,
    originalSrcset
  };
};

// Predefined responsive configurations for common use cases
export const RESPONSIVE_CONFIGS = {
  // Product detail main image - Enhanced for high-resolution displays
  productMain: {
    widths: [640, 1280, 1600, 2048, 2560], // Added ultra-high-res variants for large monitors
    sizes: '(max-width: 768px) 100vw, (max-width: 1440px) 1280px, (max-width: 2560px) 1600px, (max-width: 3440px) 2048px, 2560px'
  },
  // Product card images - Added retina support
  productCard: {
    widths: [400, 800], // Added 2x density for retina displays
    sizes: '(max-width: 768px) 400px, 400px'
  },
  // Thumbnail images - Added retina support
  thumbnail: {
    widths: [160, 320], // Added 2x density for crisp thumbnails
    sizes: '160px'
  },
  // Hero/banner images - Enhanced for very large displays
  hero: {
    widths: [640, 768, 1024, 1280, 1536, 1920, 2560, 3440],
    sizes: '100vw'
  }
};

/**
 * Generates preload link objects for critical images to be used in DocumentHead.
 * This enables SSR preloading of above-the-fold images for better LCP performance.
 * 
 * @param src - The image source URL
 * @param responsive - Responsive configuration key or 'none'
 * @param formats - Array of formats to preload (defaults to ['avif', 'webp', 'original'])
 * @returns Array of link objects for DocumentHead
 * 
 * @example
 * // In a route file:
 * export const head: DocumentHead = () => ({
 *   links: [
 *     ...generateImagePreloadLinks(productImage, 'productMain'),
 *     // other links...
 *   ]
 * });
 */
export const generateImagePreloadLinks = (
  src: string,
  responsive: keyof typeof RESPONSIVE_CONFIGS | 'none' = 'none',
  formats: ('avif' | 'webp' | 'original')[] = ['avif', 'webp', 'original']
) => {
  if (!src) return [];

  const config = responsive !== 'none' ? RESPONSIVE_CONFIGS[responsive] : null;
  const imageSources = generateResponsiveSources(
    src, 
    config?.widths || [320, 640, 1024, 1280]
  );

  const links: Array<{ 
    rel: string; 
    as: string; 
    href: string; 
    type?: string; 
    imagesrcset?: string; 
    imagesizes?: string;
    crossorigin?: string;
  }> = [];

  // Add preload links for each requested format
  if (formats.includes('avif') && imageSources.avif) {
    links.push({
      rel: 'preload',
      as: 'image',
      href: imageSources.avif,
      type: 'image/avif',
      imagesrcset: imageSources.avifSrcset || undefined,
      imagesizes: config?.sizes,
      crossorigin: (src.includes('damneddesigns.com/assetspreview') || !src.startsWith('/')) ? 'anonymous' : undefined,
    });
  }

  if (formats.includes('webp') && imageSources.webp) {
    links.push({
      rel: 'preload',
      as: 'image',
      href: imageSources.webp,
      type: 'image/webp',
      imagesrcset: imageSources.webpSrcset || undefined,
      imagesizes: config?.sizes,
      crossorigin: (src.includes('damneddesigns.com/assetspreview') || !src.startsWith('/')) ? 'anonymous' : undefined,
    });
  }

  if (formats.includes('original')) {
    links.push({
      rel: 'preload',
      as: 'image',
      href: imageSources.original,
      imagesrcset: imageSources.originalSrcset || undefined,
      imagesizes: config?.sizes,
      crossorigin: (src.includes('damneddesigns.com/assetspreview') || !src.startsWith('/')) ? 'anonymous' : undefined,
    });
  }

  return links;
};

interface OptimizedImageProps { 
 src: string;
 alt: string;
 class?: string;
 loading?: 'lazy' | 'eager';
 priority?: boolean;
 width?: number;
 height?: number;
 onLoad?: QRL<() => void>;
 onClick$?: QRL<(event: Event) => void>;
 // Responsive image props
 srcset?: string; // Custom srcset override
 sizes?: string;  // Custom sizes override
 responsive?: keyof typeof RESPONSIVE_CONFIGS | 'none'; // Predefined responsive config
}

/**
 * Optimized image component with progressive loading, spinner, and error fallback.
 * Supports both lazy loading (for below-the-fold images) and eager loading with preloading (for above-the-fold critical images).
 * Handles both Vite-processed image URLs and regular string sources.
 * 
 * New responsive image features:
 * - Use `responsive` prop with predefined configs: 'productMain', 'productCard', 'thumbnail', 'hero'
 * - Or use custom `srcset` and `sizes` props for full control
 * - Automatically generates multiple image widths and formats (AVIF, WebP, JPEG) for Vendure assets
 *
 * @param src - The image source URL.
 * @param alt - The alt text for the image.
 * @param class - Optional CSS classes for the container div.
 * @param loading - Set to 'eager' for above-the-fold images, 'lazy' (default) for below-the-fold.
 * @param priority - Set to true for critical, above-the-fold images to enable preloading and synchronous decoding.
 * @param width - The intrinsic width of the image.
 * @param height - The intrinsic height of the image.
 * @param onLoad - Optional QRL to execute when the image successfully loads.
 * @param responsive - Use predefined responsive config: 'productMain', 'productCard', 'thumbnail', 'hero', or 'none'
 * @param srcset - Custom srcset override (advanced usage)
 * @param sizes - Custom sizes override (advanced usage)
 *
 * Examples:
 * - Product detail: `<OptimizedImage src={asset.preview} responsive="productMain" />`
 * - Product card: `<OptimizedImage src={asset.preview} responsive="productCard" />`
 * - Thumbnail: `<OptimizedImage src={asset.preview} responsive="thumbnail" />`
 * - Hero image: `<OptimizedImage src={heroImage} responsive="hero" priority loading="eager" />`
 */
export const OptimizedImage = component$<OptimizedImageProps>(({
 src, 
 alt, 
 class: className = '', 
 loading = 'lazy',
 priority = false,
 width,
 height,
 onLoad,
 onClick$,
 srcset: customSrcset,
 sizes: customSizes,
 responsive = 'none',
 ...rest
}) => {
 const isLoaded = useSignal(false);
 const hasError = useSignal(false);
 // Start with a synchronous cache check for immediate feedback
 const initialCached = src ? isImageLikelyCached(src) : false;
 const shouldShowLoading = useSignal(!initialCached && !priority);
 
 // If already cached, set as loaded immediately
 if (initialCached) {
   isLoaded.value = true;
 }

 // Safari CORS fix: Add crossorigin attribute for external domain images
 const isCrossOrigin = src && (src.includes('damneddesigns.com/assetspreview') || !src.startsWith('/'));
 
  // Generate responsive image sources
 const config = responsive !== 'none' ? RESPONSIVE_CONFIGS[responsive] : null;
 const imageSources = generateResponsiveSources(
   src, 
   config?.widths || [320, 640, 1024, 1280]
 );

 // Determine final srcset and sizes
 const finalSrcset = customSrcset || (config ? imageSources.originalSrcset : undefined);
 const finalSizes = customSizes || config?.sizes;

 // Note: SSR preloading for priority images should be implemented at the route level
 // using the DocumentHead export, not in individual components.
 
 useVisibleTask$(() => {
   // Skip async check if already determined to be cached
   if (initialCached) {
     return;
   }
   
   // Check if image is already in browser cache with async method
   if (src) {
     isImageCached(src).then((cached) => {
       if (cached) {
         // Image is cached, set as loaded immediately
         isLoaded.value = true;
         shouldShowLoading.value = false;
       } else {
         // Image is not cached
         if (!priority) {
           // Show loading for non-priority images
           shouldShowLoading.value = true;
         }
         // SSR preloading is now handled at the route level via DocumentHead export.
       }
     });
   }
 });

 const handleLoad = $(() => {
 isLoaded.value = true;
 shouldShowLoading.value = false;
 onLoad?.();
 });

 const handleError = $(() => {
 hasError.value = true;
 });

 return (
 <div class={`relative overflow-hidden ${className}`} style={width && height ? { aspectRatio: `${width}/${height}` } : {}}>
  {/* Loading placeholder - Only show for non-priority images and when shouldShowLoading is true */}
  {!isLoaded.value && !hasError.value && !priority && shouldShowLoading.value && (
  <div class="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center" style={{ aspectRatio: width && height ? `${width}/${height}` : '3/4' }}>
   <div class="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
  </div>
  )}
  
  {/* Minimal placeholder for priority images - no spinner */}
  {!isLoaded.value && !hasError.value && priority && (
  <div class="absolute inset-0 bg-gray-50" style={{ aspectRatio: width && height ? `${width}/${height}` : '3/4' }} />
  )}
  
  {/* Error fallback */}
  {hasError.value && (
  <div class="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-400" style={{ aspectRatio: width && height ? `${width}/${height}` : '3/4' }}>
   <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
   <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
   </svg>
  </div>
  )}
  
  {/* Optimized image with AVIF/WebP support and responsive srcset */}
  <picture>
    {/* AVIF source for modern browsers */}
    {imageSources.avif && (
      <source 
        srcset={imageSources.avifSrcset || imageSources.avif} 
        type="image/avif" 
        sizes={finalSizes}
      />
    )}
    {/* WebP source for wider browser support */}
    {imageSources.webp && (
      <source 
        srcset={imageSources.webpSrcset || imageSources.webp} 
        type="image/webp" 
        sizes={finalSizes}
      />
    )}
    {/* Fallback JPEG/PNG for all browsers */}
    <img
      src={imageSources.original}
      alt={alt}
      class={`w-full h-full object-cover transition-opacity duration-300 ${isLoaded.value ? 'opacity-100' : 'opacity-0'}`}
      width={width}
      height={height}
      onLoad$={handleLoad}
      onError$={handleError}
      onClick$={onClick$}
      loading={loading}
      decoding={priority ? 'sync' : 'async'}
      crossOrigin={isCrossOrigin ? 'anonymous' : undefined}
      srcset={finalSrcset || undefined}
      sizes={finalSizes || undefined}
      {...rest}
    />
  </picture>
 </div>
 );
});
