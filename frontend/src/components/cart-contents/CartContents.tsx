import { component$, useContext, useSignal, useTask$, useVisibleTask$, $ } from '@builder.io/qwik';
import { useLocation, useNavigate } from '@qwik.dev/router';
import { OptimizedImage } from '~/components/ui';
import { APP_STATE } from '~/constants';
import { Order } from '~/generated/graphql';
import { removeOrderLineMutation, adjustOrderLineMutation } from '~/providers/shop/orders/order';
import { getProductBySlug } from '~/providers/shop/products/products';
import { isCheckoutPage } from '~/utils';
import { isImageCached } from '~/utils/image-cache';
import Price from '../products/Price';
import TrashIcon from '../icons/TrashIcon';
import { useLocalCart, updateLocalCartQuantity, removeFromLocalCart } from '~/contexts/CartContext';
// import { useCartPerformanceTracking } from '~/hooks/usePerformanceTracking'; // Removed for performance

// Image preloading function for cart product links
const handleProductLinkClick = $((productSlug: string, featuredAssetPreview?: string) => {
	// Preload the larger image that will be shown on the product detail page
	const targetImageUrl = featuredAssetPreview ? featuredAssetPreview.replace('?preset=thumb', '?preset=xl') : '/asset_placeholder.webp';
	
	isImageCached(targetImageUrl).then((cached) => {
		if (!cached) {
			const img = new Image();
			img.src = targetImageUrl;
		}
	});
});

export default component$<{
	order?: Order;
}>(({ order }) => {
	const navigate = useNavigate();
	const location = useLocation();
	const appState = useContext(APP_STATE);
	const localCart = useLocalCart();
	const currentOrderLineSignal = useSignal<{ id: string; value: number }>();
	const isInEditableUrl = !isCheckoutPage(location.url.toString()) || !order;
	const currencyCode = order?.currencyCode || appState.activeOrder?.currencyCode || 'USD';
	
	// Performance tracking for cart operations - DISABLED for performance
	// const performanceTracking = useCartPerformanceTracking();
	
	// Static caches - calculated once and never re-calculated unless new items added
	const productNameCache = useSignal<Record<string, string>>({});
	const quantityOptionsCache = useSignal<Record<string, number[]>>({});
	const processedLineIds = useSignal<Set<string>>(new Set());

	useTask$(({ track, cleanup }) => {
		track(() => currentOrderLineSignal.value);
		let id: NodeJS.Timeout;
		if (currentOrderLineSignal.value) {
			id = setTimeout(async () => {
				// Track cart operation performance
				// const cartOpTimer = await performanceTracking.trackCartOperation$('update-quantity'); // Disabled
				
				try {
					// Check if we're in local cart mode or Vendure order mode
					if (localCart.isLocalMode) {
						// Use local cart service for quantity updates
						await updateLocalCartQuantity(
							localCart,
							currentOrderLineSignal.value!.id, // This will be the productVariantId for local cart
							currentOrderLineSignal.value!.value
						);
					} else {
						// Use Vendure order mutations for checkout mode
						appState.activeOrder = await adjustOrderLineMutation(
							currentOrderLineSignal.value!.id,
							currentOrderLineSignal.value!.value
						);
					}
					// await cartOpTimer.end$(); // Track successful update - DISABLED
				} catch (error) {
					console.error('Failed to update cart quantity:', error);
					// await cartOpTimer.end$(); // Track failed update - DISABLED
				}
			}, 300);
		}
		cleanup(() => {
			if (id) {
				clearTimeout(id);
			}
		});
	});

	// Process new line items ONCE when they're actually added (not on every cart refresh)
	useVisibleTask$(async () => {
		// Get current lines directly without creating reactive dependencies
		const lines = order?.lines || appState.activeOrder?.lines || [];
		
		for (const line of lines) {
			// Skip if we've already processed this line
			if (processedLineIds.value.has(line.id)) continue;
			
			// Mark this line as processed immediately
			processedLineIds.value = new Set([...processedLineIds.value, line.id]);
			
			// Calculate quantity options ONCE for this line
			const stockLevel = '3'; // Hardcoded as per business logic
			let maxQty = 3;
			
			const numericStock = parseInt(stockLevel, 10);
			if (!isNaN(numericStock)) {
				maxQty = Math.min(numericStock, 8);
				maxQty = Math.max(maxQty, line.quantity);
			}
			
			const options = Array.from({length: maxQty}, (_, i) => i + 1);
			
			// Cache the quantity options for this specific line ID
			quantityOptionsCache.value = {
				...quantityOptionsCache.value,
				[line.id]: options
			};
			
			// Fetch product name if needed (only if not already available)
			if (line.productVariant?.product?.slug && !line.productVariant.product.name) {
				const slug = line.productVariant.product?.slug;
			if (!slug) continue; // Skip if no product slug
				
				// Skip if we already have this product name in cache
				if (!productNameCache.value[slug]) {
					try {
						const product = await getProductBySlug(slug);
						if (product && product.name) {
							productNameCache.value = {
								...productNameCache.value,
								[slug]: product.name
							};
						}
					} catch (error) {
						console.error(`Error fetching product details for slug ${slug}:`, error);
					}
				}
			}
		}
	});

	return (
		<div class="flow-root mx-auto w-full">
			<ul class="-my-6 divide-y divide-gray-200 mx-auto w-full">
				{/* Render local cart items when in local mode */}
				{localCart.isLocalMode && localCart.localCart.items.map((item) => {
					const productSlug = item.productVariant.product?.slug || '';
					const productName = item.productVariant.product?.name || 
						productNameCache.value[productSlug] || 
						productSlug.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

					// Calculate line price (since local cart items don't have linePriceWithTax)
					const linePrice = item.productVariant.price * item.quantity;

					// Calculate quantity options for local cart items
					const stockLevel = item.productVariant.stockLevel || '3';
					let maxQty = 3;
					const numericStock = parseInt(stockLevel, 10);
					if (!isNaN(numericStock)) {
						maxQty = Math.min(numericStock, 8);
						maxQty = Math.max(maxQty, item.quantity);
					}
					const quantityOptions = Array.from({length: maxQty}, (_, i) => i + 1);

					return (
						<li key={item.productVariantId} class="py-6 flex items-center w-full">
							{/* Product image */}
							<div class="shrink-0 w-24 border border-gray-200 rounded-md overflow-hidden">
								<div class="relative aspect-4/5">
									<OptimizedImage
										class="w-full h-full object-center object-cover"
										src={`${item.productVariant.featuredAsset?.preview || '/asset_placeholder.webp'}?preset=thumb`}
										width={160}
										height={200}
										loading="lazy"
										alt={`Image of: ${item.productVariant.name}`}
									/>
								</div>
							</div>

							{/* Product Details */}
							<div class="ml-4 flex-1 flex flex-col justify-center w-full">
								{/* Top row: titles and price */}
								<div class="flex justify-between items-start mb-1">
									{/* Product title */}
									<div class="flex flex-col">
										<h3 class="text-lg sm:text-xl font-bold text-gray-900">
											{productSlug ? (
												<a 
													href={`/products/${productSlug}/`}
													onClick$={() => handleProductLinkClick(productSlug, item.productVariant.featuredAsset?.preview)}
												>
													{productName}
												</a>
											) : (
												<span>{productName}</span>
											)}
										</h3>
										<p class="text-sm text-gray-600">{item.productVariant.name}</p>
									</div>
									
									{/* Price aligned right */}
									<div class="text-right">
										<Price
											priceWithTax={linePrice}
											currencyCode={currencyCode}
										/>
									</div>
								</div>
								
								{/* Bottom row: quantity and remove button */}
								<div class="flex items-center justify-between mt-2">
									{/* Quantity selector */}
									<div>
										{isInEditableUrl ? (
											<select
												disabled={!isInEditableUrl}
												id={`quantity-${item.productVariantId}`}
												name={`quantity-${item.productVariantId}`}
												value={item.quantity}
												onChange$={(_, el) => {
													currentOrderLineSignal.value = { id: item.productVariantId, value: +el.value };
												}}
												class="rounded-md border border-gray-300 py-1.5 text-base leading-5 font-medium text-gray-700 text-left shadow-xs focus:outline-hidden focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
											>
												{quantityOptions.map(num => (
													<option key={num} value={num} selected={item.quantity === num}>
														{num.toString()}
													</option>
												))}
											</select>
										) : (
											<span class="font-medium">{item.quantity}</span>
										)}
									</div>
									
									{/* Trash icon for removal */}
									{isInEditableUrl && (
										<button
											value={item.productVariantId}
											aria-label="Remove item"
											class="p-1 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50 transition-colors cursor-pointer"
											onClick$={async () => {
												// const removeTimer = await performanceTracking.trackCartOperation$('remove-item'); // DISABLED
												try {
													await removeFromLocalCart(localCart, item.productVariantId);
													// await removeTimer.end$(); // Track successful removal - DISABLED
												} catch (error) {
													console.error('Failed to remove item from cart:', error);
													// await removeTimer.end$(); // Track failed removal - DISABLED
												}
											}}
										>
											<TrashIcon />
										</button>
									)}
								</div>
							</div>
						</li>
					);
				})}

				{/* Render Vendure order lines when in Vendure mode */}
				{!localCart.isLocalMode && (order?.lines || appState.activeOrder?.lines || []).map((line) => {
					const { linePriceWithTax } = line;
					
					// Get product name directly without computed signal
					const productSlug = line.productVariant.product?.slug || '';
					const productName = line.productVariant.product?.name || 
						productNameCache.value[productSlug] || 
						productSlug.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

					return (
						<li key={line.id} class="py-6 flex items-center w-full">
							{/* Product image */}
							<div class="shrink-0 w-24 border border-gray-200 rounded-md overflow-hidden">
								<div class="relative aspect-4/5">
									<OptimizedImage
										class="w-full h-full object-center object-cover"
										src={`${line.featuredAsset?.preview}?preset=thumb`}
										width={160}
										height={200}
										loading="lazy"
										alt={`Image of: ${line.productVariant.name}`}
									/>
								</div>
							</div>

							{/* Product Details */}
							<div class="ml-4 flex-1 flex flex-col justify-center w-full">
								{/* Top row: titles and price */}
								<div class="flex justify-between items-start mb-1">
									{/* Product title */}
									<div class="flex flex-col">
										<h3 class="text-lg sm:text-xl font-bold text-gray-900">
											{line.productVariant.product?.slug ? (
												<a 
													href={`/products/${line.productVariant.product.slug}/`}
													onClick$={() => handleProductLinkClick(line.productVariant.product.slug, line.featuredAsset?.preview)}
												>
													{productName}
												</a>
											) : (
												<span>{productName}</span>
											)}
										</h3>
										<p class="text-sm text-gray-600">{line.productVariant.name}</p>
									</div>
									
									{/* Price aligned right */}
									<div class="text-right">
										<Price
											priceWithTax={linePriceWithTax}
											currencyCode={currencyCode}
										/>
									</div>
								</div>
								

								
								{/* Bottom row: quantity and remove button */}
								<div class="flex items-center justify-between mt-2">
									{/* Quantity selector with no label */}
									<div>
										{isInEditableUrl ? (
											<select
												disabled={!isInEditableUrl}
												id={`quantity-${line.id}`}
												name={`quantity-${line.id}`}
												value={line.quantity}
												onChange$={(_, el) => {
													currentOrderLineSignal.value = { id: line.id, value: +el.value };
												}}
												class="rounded-md border border-gray-300 py-1.5 text-base leading-5 font-medium text-gray-700 text-left shadow-xs focus:outline-hidden focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
											>
												{(quantityOptionsCache.value[line.id] || [1, 2, 3]).map(num => (
													<option key={num} value={num} selected={line.quantity === num}>
														{num.toString()}
													</option>
												))}
											</select>
										) : (
											<span class="font-medium">{line.quantity}</span>
										)}
									</div>
									
									{/* Trash icon for removal */}
									{isInEditableUrl && (
										<button
											value={line.id}
											aria-label="Remove item"
											class="p-1 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50 transition-colors cursor-pointer"
											onClick$={async () => {
												// const removeTimer = await performanceTracking.trackCartOperation$('remove-item'); // DISABLED
												try {
													// Check if we're in local cart mode
													if (localCart.isLocalMode) {
														// Use local cart service for removal
														await removeFromLocalCart(localCart, line.productVariant.id);
													} else {
														// Use Vendure order mutations for checkout mode
														appState.activeOrder = await removeOrderLineMutation(line.id);
														if (
															appState.activeOrder?.lines?.length === 0 &&
															isCheckoutPage(location.url.toString())
														) {
															appState.showCart = false;
															navigate(`/shop`);
														}
													}
													// await removeTimer.end$(); // Track successful removal - DISABLED
												} catch (error) {
													console.error('Failed to remove item from cart:', error);
													// await removeTimer.end$(); // Track failed removal - DISABLED
												}
											}}
										>
											<TrashIcon />
										</button>
									)}
								</div>
							</div>
						</li>
					);
				})}
			</ul>
		</div>
	);
});