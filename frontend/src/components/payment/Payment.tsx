import { component$, QRL, useSignal, useVisibleTask$, Signal } from '@qwik.dev/core'; // Added Signal type
import { getEligiblePaymentMethodsQuery } from '~/providers/shop/checkout/checkout';
import { EligiblePaymentMethods } from '~/types';
import NMI from './NMI';

interface PaymentProps {
 onForward$: QRL<(orderCode: string) => void>; // Expects orderCode from NMI
 onError$: QRL<(errorMessage: string) => void>; // For NMI to report errors
 onProcessingChange$?: QRL<(isProcessing: boolean) => void>; // For NMI to report processing state changes
 triggerNMISignal: Signal<number>; // Signal from parent to trigger NMI submission
 isDisabled?: boolean;
 hideButton?: boolean;
}

export default component$<PaymentProps>(({ onForward$, onError$, onProcessingChange$, triggerNMISignal, isDisabled, hideButton = false }) => {
	console.log('[Payment] Component rendering with props:', { isDisabled, hideButton });
	const paymentMethods = useSignal<EligiblePaymentMethods[]>();

	useVisibleTask$(async () => {
		console.log('[Payment] Loading eligible payment methods...');
		try {
			paymentMethods.value = await getEligiblePaymentMethodsQuery();
			console.log('[Payment] Payment methods loaded:', paymentMethods.value);
		} catch (error) {
			console.error('[Payment] Error loading payment methods:', error);
		}
	});

	return (
		<div class={`flex flex-col space-y-2 items-center ${isDisabled ? 'opacity-50 pointer-events-none' : ''}`}>
			{/* Show payment form immediately - don't wait for payment methods API */}
			<div class="flex flex-col items-center w-full">
				<div class="w-full">
					<NMI 
						isDisabled={isDisabled} 
						onForward$={onForward$} 
						onError$={onError$}
						onProcessingChange$={onProcessingChange$}
						hideButton={hideButton} 
						triggerSignal={triggerNMISignal} 
					/>
				</div>
			</div>
			
			{/* Fallback: If payment methods are loaded and NMI is available, show it */}
			{paymentMethods.value?.map((method) => (
				<div key={method.code} class="flex flex-col items-center w-full" style={{ display: 'none' }}>
					{method.code === 'nmi' && (
						<div class="w-full">
							{/* This is hidden since we show NMI above */}
						</div>
					)}
				</div>
			))}
		</div>
	);
});
