/**
 * Geolocation service for detecting user's country based on IP address
 * Falls back gracefully to US for 90% of customers
 */

export interface GeolocationResult {
  countryCode: string;
  country: string;
  confidence: 'high' | 'medium' | 'low' | 'fallback';
  source: 'ip' | 'browser' | 'fallback';
}

/**
 * Get country code from IP address using multiple free services
 */
export async function getCountryFromIP(): Promise<GeolocationResult> {
  // Try multiple free IP geolocation services in order of preference
  const services = [
    {
      url: 'https://ipapi.co/json/',
      parser: (data: any) => ({
        countryCode: data.country_code || 'US',
        country: data.country_name || 'United States',
      })
    },
    {
      url: 'https://api.country.is/',
      parser: (data: any) => ({
        countryCode: data.country || 'US',
        country: data.country || 'United States',
      })
    },
    {
      url: 'https://get.geojs.io/v1/ip/country.json',
      parser: (data: any) => ({
        countryCode: data.country || 'US',
        country: data.name || 'United States',
      })
    }
  ];

  for (const service of services) {
    try {
      const response = await fetch(service.url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        // Short timeout to avoid blocking the checkout
        signal: AbortSignal.timeout(3000)
      });

      if (response.ok) {
        const data = await response.json();
        const result = service.parser(data);
        
        if (result.countryCode && result.countryCode !== 'XX') {
          return {
            countryCode: result.countryCode.toUpperCase(),
            country: result.country,
            confidence: 'high',
            source: 'ip'
          };
        }
      }
    } catch (error) {
      console.warn(`Geolocation service failed:`, error);
      // Continue to next service
    }
  }

  // All services failed, return US fallback for 90% of customers
  return {
    countryCode: 'US',
    country: 'United States',
    confidence: 'fallback',
    source: 'fallback'
  };
}

/**
 * Get country from browser geolocation API (requires user permission)
 * This is less reliable but can be used as a secondary method
 */
export async function getCountryFromBrowser(): Promise<GeolocationResult | null> {
  if (!navigator.geolocation) {
    return null;
  }

  return new Promise((resolve) => {
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          // Use reverse geocoding to get country from coordinates
          const response = await fetch(
            `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`,
            { signal: AbortSignal.timeout(3000) }
          );
          
          if (response.ok) {
            const data = await response.json();
            resolve({
              countryCode: data.countryCode || 'US',
              country: data.countryName || 'United States',
              confidence: 'medium',
              source: 'browser'
            });
          } else {
            resolve(null);
          }
        } catch (error) {
          console.warn('Browser geolocation reverse geocoding failed:', error);
          resolve(null);
        }
      },
      () => resolve(null), // User denied permission or error
      { 
        timeout: 5000,
        enableHighAccuracy: false,
        maximumAge: 600000 // 10 minutes cache
      }
    );
  });
}

/**
 * Main function to detect user's country with multiple fallbacks
 */
export async function detectUserCountry(): Promise<GeolocationResult> {
  // First try IP-based detection (most reliable and doesn't require permissions)
  try {
    const ipResult = await getCountryFromIP();
    if (ipResult.confidence !== 'fallback') {
      return ipResult;
    }
  } catch (error) {
    console.warn('IP-based geolocation failed:', error);
  }

  // If IP detection failed, try browser geolocation as backup
  try {
    const browserResult = await getCountryFromBrowser();
    if (browserResult) {
      return browserResult;
    }
  } catch (error) {
    console.warn('Browser geolocation failed:', error);
  }

  // Final fallback to US (for 90% of customers)
  return {
    countryCode: 'US',
    country: 'United States',
    confidence: 'fallback',
    source: 'fallback'
  };
}

/**
 * Cache the geolocation result for the session to avoid repeated API calls
 */
let cachedResult: GeolocationResult | null = null;

export async function getCachedUserCountry(): Promise<GeolocationResult> {
  if (cachedResult) {
    return cachedResult;
  }

  cachedResult = await detectUserCountry();
  return cachedResult;
}
