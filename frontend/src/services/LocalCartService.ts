// Import order mutations dynamically to avoid circular dependencies
import { Order } from '~/generated/graphql';

// LocalCart Interface - Cart stored in localStorage until checkout
export interface LocalCartItem {
  productVariantId: string;
  quantity: number;
  lastStockCheck?: number; // Timestamp of last stock validation
  productVariant: {
    id: string;
    name: string;
    price: number;
    stockLevel?: string;
    product: {
      id: string;
      name: string;
      slug: string;
    };
    options: {
      id: string;
      name: string;
      group: {
        name: string;
      };
    }[];
    featuredAsset?: {
      id: string;
      preview: string;
    } | null;
  };
}

export interface LocalCart {
  items: LocalCartItem[];
  totalQuantity: number;
  subTotal: number;
  currencyCode: string;
}

export interface StockValidationResult {
  success: boolean;
  availableStock: number;
  adjustedQuantity?: number;
  error?: string;
}

export interface ValidationErrors {
  valid: boolean;
  errors: string[];
}

// LocalCart Service
export class LocalCartService {
  private static readonly CART_KEY = 'vendure_local_cart';
  private static readonly STOCK_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Get cart from localStorage
  static getCart(): LocalCart {
    if (typeof window === 'undefined') {
      return {
        items: [],
        totalQuantity: 0,
        subTotal: 0,
        currencyCode: 'USD'
      };
    }

    try {
      const stored = localStorage.getItem(this.CART_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to parse cart from localStorage:', error);
    }

    return {
      items: [],
      totalQuantity: 0,
      subTotal: 0,
      currencyCode: 'USD'
    };
  }

  // Save cart to localStorage
  static saveCart(cart: LocalCart): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(this.CART_KEY, JSON.stringify(cart));
      } catch (error) {
        console.error('Failed to save cart to localStorage:', error);
      }
    }
  }

  // Validate stock level for a product variant using stored data
  static validateStockLevel(item: LocalCartItem, requestedQuantity: number): StockValidationResult {
    try {
      // Use stock level from cached product variant data
      const availableStock = parseInt(item.productVariant.stockLevel || '0');
      
      if (requestedQuantity <= availableStock) {
        return {
          success: true,
          availableStock
        };
      } else {
        return {
          success: false,
          availableStock,
          adjustedQuantity: Math.max(0, availableStock),
          error: `Only ${availableStock} items available`
        };
      }
    } catch (error) {
      console.error('Stock validation failed:', error);
      return {
        success: false,
        availableStock: 0,
        adjustedQuantity: 0,
        error: 'Stock validation failed'
      };
    }
  }

  // Add item to cart with stock validation
  static addItem(item: LocalCartItem): { cart: LocalCart; stockResult: StockValidationResult } {
    const cart = this.getCart();
    const existingIndex = cart.items.findIndex(i => i.productVariantId === item.productVariantId);
    
    const newQuantity = existingIndex >= 0 
      ? cart.items[existingIndex].quantity + item.quantity
      : item.quantity;
    
    // Validate stock before adding
    const stockResult = this.validateStockLevel(item, newQuantity);
    
    if (existingIndex >= 0) {
      // Update existing item with validated quantity
      cart.items[existingIndex].quantity = stockResult.adjustedQuantity ?? newQuantity;
      cart.items[existingIndex].lastStockCheck = Date.now();
      // Update stock level info
      cart.items[existingIndex].productVariant.stockLevel = stockResult.availableStock.toString();
    } else {
      // Add new item with validated quantity
      item.quantity = stockResult.adjustedQuantity ?? newQuantity;
      item.lastStockCheck = Date.now();
      item.productVariant.stockLevel = stockResult.availableStock.toString();
      cart.items.push(item);
    }
    
    this.recalculateTotals(cart);
    this.saveCart(cart);
    return { cart, stockResult };
  }

  // Update item quantity with stock validation  
  static updateItemQuantity(productVariantId: string, quantity: number): { cart: LocalCart; stockResult: StockValidationResult } {
    const cart = this.getCart();
    const itemIndex = cart.items.findIndex(item => item.productVariantId === productVariantId);
    
    if (itemIndex === -1) {
      // Item not found, return current cart
      return { 
        cart, 
        stockResult: { success: true, availableStock: 0 }
      };
    }
    
    // Validate stock before updating
    const stockResult = this.validateStockLevel(cart.items[itemIndex], quantity);
    
    // Update with validated quantity
    cart.items[itemIndex].quantity = stockResult.adjustedQuantity ?? quantity;
    cart.items[itemIndex].lastStockCheck = Date.now();
    cart.items[itemIndex].productVariant.stockLevel = stockResult.availableStock.toString();
    
    this.recalculateTotals(cart);
    this.saveCart(cart);
    return { cart, stockResult };
  }

  // Remove item from cart
  static removeItem(productVariantId: string): LocalCart {
    const cart = this.getCart();
    cart.items = cart.items.filter(item => item.productVariantId !== productVariantId);
    this.recalculateTotals(cart);
    this.saveCart(cart);
    return cart;
  }

  // Check if stock validation is needed (based on cache time)
  static isStockCheckNeeded(item: LocalCartItem): boolean {
    if (!item.lastStockCheck) return true;
    return Date.now() - item.lastStockCheck > this.STOCK_CACHE_DURATION;
  }

  // Refresh stock levels for all cart items (call periodically)
  static refreshAllStockLevels(): LocalCart {
    const cart = this.getCart();
    cart.items.forEach((item) => {
      if (this.isStockCheckNeeded(item)) {
        const stockResult = this.validateStockLevel(item, item.quantity);
        
        // Update stock info and adjust quantity if needed
        item.productVariant.stockLevel = stockResult.availableStock.toString();
        item.lastStockCheck = Date.now();
        
        if (!stockResult.success && stockResult.adjustedQuantity !== undefined) {
          item.quantity = stockResult.adjustedQuantity;
        }
      }
    });
    
    this.recalculateTotals(cart);
    this.saveCart(cart);
    return cart;
  }

  // Validate all cart items for checkout
  static validateStock(): ValidationErrors {
    const cart = this.getCart();
    const errors: string[] = [];
    
    for (const item of cart.items) {
      const stockLevel = parseInt(item.productVariant.stockLevel || '0');
      if (item.quantity > stockLevel) {
        errors.push(
          `${item.productVariant.name}: Only ${stockLevel} available (you have ${item.quantity})`
        );
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Clear cart
  static clearCart(): LocalCart {
    const emptyCart: LocalCart = {
      items: [],
      totalQuantity: 0,
      subTotal: 0,
      currencyCode: 'USD'
    };
    this.saveCart(emptyCart);
    return emptyCart;
  }

  // Recalculate cart totals
  static recalculateTotals(cart: LocalCart): void {
    cart.totalQuantity = cart.items.reduce((total, item) => total + item.quantity, 0);
    cart.subTotal = cart.items.reduce((total, item) => {
      return total + (item.productVariant.price * item.quantity);
    }, 0);
  }

  // Convert local cart to Vendure order
  static async convertToVendureOrder(): Promise<Order | null> {
    console.log('Starting cart to order conversion process...');
    try {
      const cart = this.getCart();
      console.log('Cart state before conversion:', JSON.stringify(cart, null, 2));
      if (cart.items.length === 0) {
        console.log('Conversion skipped: cart is empty.');
        return null;
      }
      
      const validation = this.validateStock();
      if (!validation.valid) {
        throw new Error(`Some items in your cart are out of stock: ${validation.errors.join(', ')}`);
      }
      
      // Dynamically import to avoid circular dependencies
      const { addItemToOrderMutation } = await import('~/providers/shop/orders/order');
      let order: Order | null = null;
      
      for (const item of cart.items) {
        try {
          const result = await addItemToOrderMutation(item.productVariantId, item.quantity);
          if (result && '__typename' in result && result.__typename === 'Order') {
            order = result as Order;
            console.log(`Successfully added item ${item.productVariantId}. Order state:`, JSON.stringify(order, null, 2));
          } else {
            console.error(`Failed to add item ${item.productVariantId}, received:`, result);
          }
        } catch (itemError) {
          console.error(`Error adding item ${item.productVariantId}:`, itemError);
          // Continue with other items even if one fails
        }
      }
      
      // Clear local cart after successful conversion
      if (order) {
        this.clearCart();
        console.log('Order created successfully with', order.lines?.length || 0, 'items');
      } else {
        throw new Error('Failed to create order with items');
      }
      
      return order;
    } catch (error) {
      console.error('Failed to convert cart to Vendure order:', error);
      throw error;
    }
  }
}
