import { createContextId, useContext, useContextProvider, useStore, Slot, component$ } from '@builder.io/qwik';

// Define the validation state structure
export interface CheckoutValidationState {
  // Customer validation
  isCustomerValid: boolean;
  customerErrors: {
    email?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
  
  // Address validation
  isShippingAddressValid: boolean;
  shippingAddressErrors: {
    streetLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    countryCode?: string;
  };
  
  // Billing address validation (when different billing is used)
  isBillingAddressValid: boolean;
  billingAddressErrors: {
    firstName?: string;
    lastName?: string;
    streetLine1?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    countryCode?: string;
  };
  useDifferentBilling: boolean;
  
  // Payment validation
  isPaymentValid: boolean;
  paymentErrors: {
    cardNumber?: string;
    expiryDate?: string;
    cvv?: string;
  };
  
  // Terms & Conditions validation
  isTermsAccepted: boolean;
  
  // Overall validation state
  isAllValid: boolean;
  
  // Validation touched states to know if user has interacted with forms
  customerTouched: boolean;
  shippingAddressTouched: boolean;
  billingAddressTouched: boolean;
  paymentTouched: boolean;
}

// Define methods to update validation state
export interface CheckoutValidationActions {
  updateCustomerValidation: (isValid: boolean, errors: CheckoutValidationState['customerErrors'], touched?: boolean) => void;
  updateShippingAddressValidation: (isValid: boolean, errors: CheckoutValidationState['shippingAddressErrors'], touched?: boolean) => void;
  updateBillingAddressValidation: (isValid: boolean, errors: CheckoutValidationState['billingAddressErrors'], touched?: boolean) => void;
  updatePaymentValidation: (isValid: boolean, errors: CheckoutValidationState['paymentErrors'], touched?: boolean) => void;
  updateBillingMode: (useDifferentBilling: boolean) => void;
  updateTermsAcceptance: (isAccepted: boolean) => void;
  resetValidation: () => void;
}

// Combined context type
export type CheckoutValidationContext = CheckoutValidationState & CheckoutValidationActions;

// Create the context
export const CheckoutValidationContextId = createContextId<CheckoutValidationContext>('checkout-validation');

// Hook to use the context
export const useCheckoutValidation = () => useContext(CheckoutValidationContextId);

// Initial state
const createInitialState = (): CheckoutValidationState => ({
  isCustomerValid: false,
  customerErrors: {},
  isShippingAddressValid: false,
  shippingAddressErrors: {},
  isBillingAddressValid: true, // Valid by default since it's optional
  billingAddressErrors: {},
  useDifferentBilling: false,
  isPaymentValid: false,
  paymentErrors: {},
  isTermsAccepted: false,
  isAllValid: false,
  customerTouched: false,
  shippingAddressTouched: false,
  billingAddressTouched: false,
  paymentTouched: false,
});

// Provider component
export const CheckoutValidationProvider = component$(() => {
  const state = useStore<CheckoutValidationState>(createInitialState());
  
  // Function to recalculate overall validation
  const recalculateOverallValidation = () => {
    const customerValid = state.isCustomerValid;
    const shippingValid = state.isShippingAddressValid;
    const billingValid = state.useDifferentBilling ? state.isBillingAddressValid : true;
    const paymentValid = state.isPaymentValid;
    const termsValid = state.isTermsAccepted;
    
    state.isAllValid = customerValid && shippingValid && billingValid && paymentValid && termsValid;
    
    console.log('[CheckoutValidation] Overall validation recalculated:', {
      customer: customerValid,
      shipping: shippingValid,
      billing: billingValid,
      payment: paymentValid,
      terms: termsValid,
      overall: state.isAllValid
    });
  };
  
  const actions: CheckoutValidationActions = {
    updateCustomerValidation: (isValid: boolean, errors: CheckoutValidationState['customerErrors'], touched = true) => {
      state.isCustomerValid = isValid;
      state.customerErrors = errors;
      if (touched) state.customerTouched = true;
      recalculateOverallValidation();
    },
    
    updateShippingAddressValidation: (isValid: boolean, errors: CheckoutValidationState['shippingAddressErrors'], touched = true) => {
      state.isShippingAddressValid = isValid;
      state.shippingAddressErrors = errors;
      if (touched) state.shippingAddressTouched = true;
      recalculateOverallValidation();
    },
    
    updateBillingAddressValidation: (isValid: boolean, errors: CheckoutValidationState['billingAddressErrors'], touched = true) => {
      state.isBillingAddressValid = isValid;
      state.billingAddressErrors = errors;
      if (touched) state.billingAddressTouched = true;
      recalculateOverallValidation();
    },
    
    updatePaymentValidation: (isValid: boolean, errors: CheckoutValidationState['paymentErrors'], touched = true) => {
      state.isPaymentValid = isValid;
      state.paymentErrors = errors;
      if (touched) state.paymentTouched = true;
      recalculateOverallValidation();
    },
    
    updateBillingMode: (useDifferentBilling: boolean) => {
      state.useDifferentBilling = useDifferentBilling;
      // If switching to same billing, mark billing as valid
      if (!useDifferentBilling) {
        state.isBillingAddressValid = true;
        state.billingAddressErrors = {};
      }
      recalculateOverallValidation();
    },
    
    updateTermsAcceptance: (isAccepted: boolean) => {
      state.isTermsAccepted = isAccepted;
      recalculateOverallValidation();
    },
    
    resetValidation: () => {
      Object.assign(state, createInitialState());
    }
  };
  
  // Create a reactive context value that maintains store reactivity
  const contextValue = useStore<CheckoutValidationContext>({
    // State properties (reactive)
    get isCustomerValid() { return state.isCustomerValid; },
    get customerErrors() { return state.customerErrors; },
    get isShippingAddressValid() { return state.isShippingAddressValid; },
    get shippingAddressErrors() { return state.shippingAddressErrors; },
    get isBillingAddressValid() { return state.isBillingAddressValid; },
    get billingAddressErrors() { return state.billingAddressErrors; },
    get useDifferentBilling() { return state.useDifferentBilling; },
    get isPaymentValid() { return state.isPaymentValid; },
    get paymentErrors() { return state.paymentErrors; },
    get isTermsAccepted() { return state.isTermsAccepted; },
    get isAllValid() { return state.isAllValid; },
    get customerTouched() { return state.customerTouched; },
    get shippingAddressTouched() { return state.shippingAddressTouched; },
    get billingAddressTouched() { return state.billingAddressTouched; },
    get paymentTouched() { return state.paymentTouched; },
    
    // Action methods
    updateCustomerValidation: actions.updateCustomerValidation,
    updateShippingAddressValidation: actions.updateShippingAddressValidation,
    updateBillingAddressValidation: actions.updateBillingAddressValidation,
    updatePaymentValidation: actions.updatePaymentValidation,
    updateBillingMode: actions.updateBillingMode,
    updateTermsAcceptance: actions.updateTermsAcceptance,
    resetValidation: actions.resetValidation,
  });
  
  // Use Qwik's useContextProvider instead of React-style Provider
  useContextProvider(CheckoutValidationContextId, contextValue);
  
  return <Slot />;
});
