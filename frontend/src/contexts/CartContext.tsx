import { 
  component$, 
  createContextId, 
  useContext, 
  useContextProvider, 
  useStore, 
  useVisibleTask$,
  useComputed$,
  $,
  Slot
} from '@qwik.dev/core';
import { LocalCartService, type LocalCart, type StockValidationResult } from '~/services/LocalCartService';

// Cart Context Interface - Only store data, not functions
export interface CartContextState {
  // Cart data
  localCart: LocalCart;
  
  // State flags
  isLocalMode: boolean;
  isLoading: boolean;
  lastError: string | null;
  
  // Stock validation results
  lastStockValidation: Record<string, StockValidationResult>;
}

// Create context for state only
export const CartContextId = createContextId<CartContextState>('cart-context');

// Context Provider Component
export const CartProvider = component$(() => {
  // Initialize cart state
  const cartState = useStore<CartContextState>({
    localCart: {
      items: [],
      totalQuantity: 0,
      subTotal: 0,
      currencyCode: 'USD'
    },
    isLocalMode: true,
    isLoading: false,
    lastError: null,
    lastStockValidation: {}
  });

  // Computed values for better performance (replaces useTask$ anti-patterns)
  const cartTotal = useComputed$(() => {
    return cartState.localCart.items.reduce((sum, item) => 
      sum + (item.productVariant.price * item.quantity), 0
    );
  });

  const cartQuantity = useComputed$(() => {
    return cartState.localCart.items.reduce((sum, item) => sum + item.quantity, 0);
  });

  const cartSubTotal = useComputed$(() => {
    return cartState.localCart.items.reduce((sum, item) => 
      sum + (item.productVariant.price * item.quantity), 0
    );
  });

  // Update derived values reactively
  useVisibleTask$(({ track }) => {
    track(() => cartTotal.value);
    track(() => cartQuantity.value);
    track(() => cartSubTotal.value);
    
    // Update cart state with computed values
    cartState.localCart.totalQuantity = cartQuantity.value;
    cartState.localCart.subTotal = cartSubTotal.value;
  });

  // Load cart on mount
  useVisibleTask$(async () => {
    // Load initial cart state
    cartState.localCart = LocalCartService.getCart();
    
    // Set up periodic stock refresh (every 2 minutes)
    const stockRefreshInterval = setInterval(() => {
      if (cartState.localCart.items.length > 0 && cartState.isLocalMode) {
        try {
          cartState.localCart = LocalCartService.refreshAllStockLevels();
        } catch (error) {
          console.error('Periodic stock refresh failed:', error);
        }
      }
    }, 2 * 60 * 1000); // 2 minutes

    // Cleanup interval on unmount
    return () => {
      clearInterval(stockRefreshInterval);
    };
  });

  // Provide context
  useContextProvider(CartContextId, cartState);

  return <Slot />;
});

// Hook to use cart context
export const useLocalCart = () => {
  return useContext(CartContextId);
};

// Helper functions that can be called from components
export const addToLocalCart = $((cartState: CartContextState, item: any) => {
  cartState.isLoading = true;
  cartState.lastError = null;
  
  try {
    const result = LocalCartService.addItem(item);
    cartState.localCart = result.cart;
    cartState.lastStockValidation[item.productVariantId] = result.stockResult;
    
    if (!result.stockResult.success) {
      cartState.lastError = result.stockResult.error || 'Stock validation failed';
    }
  } catch (error) {
    cartState.lastError = error instanceof Error ? error.message : 'Failed to add item to cart';
  } finally {
    cartState.isLoading = false;
  }
});

export const updateLocalCartQuantity = $((cartState: CartContextState, productVariantId: string, quantity: number) => {
  cartState.isLoading = true;
  cartState.lastError = null;
  
  try {
    const result = LocalCartService.updateItemQuantity(productVariantId, quantity);
    cartState.localCart = result.cart;
    cartState.lastStockValidation[productVariantId] = result.stockResult;
    
    if (!result.stockResult.success) {
      cartState.lastError = result.stockResult.error || 'Stock validation failed';
    }
  } catch (error) {
    cartState.lastError = error instanceof Error ? error.message : 'Failed to update quantity';
  } finally {
    cartState.isLoading = false;
  }
});

export const removeFromLocalCart = $((cartState: CartContextState, productVariantId: string) => {
  try {
    cartState.localCart = LocalCartService.removeItem(productVariantId);
    // Clear validation for removed item
    delete cartState.lastStockValidation[productVariantId];
    cartState.lastError = null;
  } catch (error) {
    cartState.lastError = error instanceof Error ? error.message : 'Failed to remove item';
  }
});

export const convertLocalCartToVendureOrder = $(async (cartState: CartContextState) => {
  cartState.isLoading = true;
  cartState.lastError = null;
  
  try {
    // Validate stock before conversion
    const stockValidation = LocalCartService.validateStock();
    
    if (!stockValidation.valid) {
      cartState.lastError = `Stock validation failed: ${stockValidation.errors.join(', ')}`;
      return null;
    }
    
    const order = await LocalCartService.convertToVendureOrder();
    
    if (order) {
      // Switch to Vendure mode after successful conversion
      cartState.isLocalMode = false;
      cartState.localCart = LocalCartService.clearCart();
      cartState.lastStockValidation = {};
    } else {
      cartState.lastError = 'Failed to create Vendure order';
    }
    
    return order;
  } catch (error) {
    cartState.lastError = error instanceof Error ? error.message : 'Checkout failed';
    return null;
  } finally {
    cartState.isLoading = false;
  }
});
