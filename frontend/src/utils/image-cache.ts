import { $ } from '@qwik.dev/core';

/**
 * Synchronously checks if an image is likely cached
 * @param src - The image source URL to check
 * @returns boolean - true if likely cached, false if not
 */
export const isImageLikelyCached = (src: string): boolean => {
  try {
    const img = new Image();
    img.src = src;
    // If complete is true immediately and naturalWidth > 0, it's cached
    return img.complete && img.naturalWidth > 0;
  } catch {
    return false;
  }
};

/**
 * Checks if an image is already cached in the browser
 * @param src - The image source URL to check
 * @returns Promise<boolean> - true if cached, false if not
 */
export const isImageCached = $((src: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    
    // Set up event handlers before setting src
    img.onload = () => {
      // Image loaded successfully
      resolve(true);
    };
    
    img.onerror = () => {
      // Image failed to load
      resolve(false);
    };
    
    // Check if already complete before setting src (in case it's already cached)
    if (img.complete && img.naturalWidth > 0) {
      resolve(true);
      return;
    }
    
    // Set src to trigger load
    img.src = src;
    
    // Check immediately after setting src for cached images
    setTimeout(() => {
      if (img.complete && img.naturalWidth > 0) {
        resolve(true);
      }
    }, 0);
  });
});

/**
 * Preloads an image in the background for better caching
 * @param src - The image source URL to preload
 */
export const preloadImage = $((src: string) => {
  const img = new Image();
  img.src = src;
});

/**
 * Applies conditional loading state based on image cache status
 * @param src - The image source URL to check
 * @param setLoading - Function to set loading state
 * @param timeout - Timeout duration for loading state (default: 300ms)
 */
export const applyConditionalLoading = $((
  src: string, 
  setLoading: (loading: boolean) => void, 
  timeout: number = 300
) => {
  isImageCached(src).then((cached) => {
    if (!cached) {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
      }, timeout);
    }
  });
});
