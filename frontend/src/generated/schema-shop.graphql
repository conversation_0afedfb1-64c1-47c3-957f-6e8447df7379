union ActiveOrderResult = NoActiveOrderError | Order

union AddPaymentToOrderResult = IneligiblePaymentMethodError | NoActiveOrderError | Order | OrderPaymentStateError | OrderStateTransitionError | PaymentDeclinedError | PaymentFailedError

type Address implements Node {
  city: String
  company: String
  country: Country!
  createdAt: DateTime!
  customFields: JSON
  defaultBillingAddress: Boolean
  defaultShippingAddress: Boolean
  fullName: String
  id: ID!
  phoneNumber: String
  postalCode: String
  province: String
  streetLine1: String!
  streetLine2: String
  updatedAt: DateTime!
}

type Adjustment {
  adjustmentSource: String!
  amount: Money!
  data: JSON
  description: String!
  type: AdjustmentType!
}

enum AdjustmentType {
  DISTRIBUTED_ORDER_PROMOTION
  OTHER
  PROMOTION
}

"""
Returned when attempting to set the Customer for an Order when already logged in.
"""
type AlreadyLoggedInError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

union ApplyCouponCodeResult = CouponCodeExpiredError | CouponCodeInvalidError | CouponCodeLimitError | Order

type Asset implements Node {
  createdAt: DateTime!
  customFields: JSON
  fileSize: Int!
  focalPoint: Coordinate
  height: Int!
  id: ID!
  mimeType: String!
  name: String!
  preview: String!
  source: String!
  tags: [Tag!]!
  type: AssetType!
  updatedAt: DateTime!
  width: Int!
}

type AssetList implements PaginatedList {
  items: [Asset!]!
  totalItems: Int!
}

enum AssetType {
  BINARY
  IMAGE
  VIDEO
}

input AuthenticationInput {
  native: NativeAuthInput
}

type AuthenticationMethod implements Node {
  createdAt: DateTime!
  id: ID!
  strategy: String!
  updatedAt: DateTime!
}

union AuthenticationResult = CurrentUser | InvalidCredentialsError | NotVerifiedError

type BooleanCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

"""Operators for filtering on a list of Boolean fields"""
input BooleanListOperators {
  inList: Boolean!
}

"""Operators for filtering on a Boolean field"""
input BooleanOperators {
  eq: Boolean
  isNull: Boolean
}

type BooleanStructFieldConfig implements StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  type: String!
  ui: JSON
}

type Channel implements Node {
  availableCurrencyCodes: [CurrencyCode!]!
  availableLanguageCodes: [LanguageCode!]
  code: String!
  createdAt: DateTime!
  currencyCode: CurrencyCode! @deprecated(reason: "Use defaultCurrencyCode instead")
  customFields: JSON
  defaultCurrencyCode: CurrencyCode!
  defaultLanguageCode: LanguageCode!
  defaultShippingZone: Zone
  defaultTaxZone: Zone
  id: ID!

  """Not yet used - will be implemented in a future release."""
  outOfStockThreshold: Int
  pricesIncludeTax: Boolean!
  seller: Seller
  token: String!

  """Not yet used - will be implemented in a future release."""
  trackInventory: Boolean
  updatedAt: DateTime!
}

type Collection implements Node {
  assets: [Asset!]!
  breadcrumbs: [CollectionBreadcrumb!]!
  children: [Collection!]
  createdAt: DateTime!
  customFields: JSON
  description: String!
  featuredAsset: Asset
  filters: [ConfigurableOperation!]!
  id: ID!
  languageCode: LanguageCode
  name: String!
  parent: Collection
  parentId: ID!
  position: Int!
  productVariants(options: ProductVariantListOptions): ProductVariantList!
  slug: String!
  translations: [CollectionTranslation!]!
  updatedAt: DateTime!
}

type CollectionBreadcrumb {
  id: ID!
  name: String!
  slug: String!
}

input CollectionFilterParameter {
  _and: [CollectionFilterParameter!]
  _or: [CollectionFilterParameter!]
  createdAt: DateOperators
  description: StringOperators
  id: IDOperators
  languageCode: StringOperators
  name: StringOperators
  parentId: IDOperators
  position: NumberOperators
  slug: StringOperators
  updatedAt: DateOperators
}

type CollectionList implements PaginatedList {
  items: [Collection!]!
  totalItems: Int!
}

input CollectionListOptions {
  """Allows the results to be filtered"""
  filter: CollectionFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: CollectionSortParameter

  """Takes n results, for use in pagination"""
  take: Int
  topLevelOnly: Boolean
}

"""
Which Collections are present in the products returned
by the search, and in what quantity.
"""
type CollectionResult {
  collection: Collection!
  count: Int!
}

input CollectionSortParameter {
  createdAt: SortOrder
  description: SortOrder
  id: SortOrder
  name: SortOrder
  parentId: SortOrder
  position: SortOrder
  slug: SortOrder
  updatedAt: SortOrder
}

type CollectionTranslation {
  createdAt: DateTime!
  description: String!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  slug: String!
  updatedAt: DateTime!
}

type ConfigArg {
  name: String!
  value: String!
}

type ConfigArgDefinition {
  defaultValue: JSON
  description: String
  label: String
  list: Boolean!
  name: String!
  required: Boolean!
  type: String!
  ui: JSON
}

input ConfigArgInput {
  name: String!

  """A JSON stringified representation of the actual value"""
  value: String!
}

type ConfigurableOperation {
  args: [ConfigArg!]!
  code: String!
}

type ConfigurableOperationDefinition {
  args: [ConfigArgDefinition!]!
  code: String!
  description: String!
}

input ConfigurableOperationInput {
  arguments: [ConfigArgInput!]!
  code: String!
}

type Coordinate {
  x: Float!
  y: Float!
}

"""
A Country of the world which your shop operates in.

The `code` field is typically a 2-character ISO code such as "GB", "US", "DE" etc. This code is used in certain inputs such as
`UpdateAddressInput` and `CreateAddressInput` to specify the country.
"""
type Country implements Node & Region {
  code: String!
  createdAt: DateTime!
  customFields: JSON
  enabled: Boolean!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  parent: Region
  parentId: ID
  translations: [RegionTranslation!]!
  type: String!
  updatedAt: DateTime!
}

type CountryList implements PaginatedList {
  items: [Country!]!
  totalItems: Int!
}

"""Returned if the provided coupon code is invalid"""
type CouponCodeExpiredError implements ErrorResult {
  couponCode: String!
  errorCode: ErrorCode!
  message: String!
}

"""Returned if the provided coupon code is invalid"""
type CouponCodeInvalidError implements ErrorResult {
  couponCode: String!
  errorCode: ErrorCode!
  message: String!
}

"""Returned if the provided coupon code is invalid"""
type CouponCodeLimitError implements ErrorResult {
  couponCode: String!
  errorCode: ErrorCode!
  limit: Int!
  message: String!
}

"""
Input used to create an Address.

The countryCode must correspond to a `code` property of a Country that has been defined in the
Vendure server. The `code` property is typically a 2-character ISO code such as "GB", "US", "DE" etc.
If an invalid code is passed, the mutation will fail.
"""
input CreateAddressInput {
  city: String
  company: String
  countryCode: String!
  customFields: JSON
  defaultBillingAddress: Boolean
  defaultShippingAddress: Boolean
  fullName: String
  phoneNumber: String
  postalCode: String
  province: String
  streetLine1: String!
  streetLine2: String
}

input CreateCustomerInput {
  customFields: JSON
  emailAddress: String!
  firstName: String!
  lastName: String!
  phoneNumber: String
  title: String
}

"""
@description
ISO 4217 currency code

@docsCategory common
"""
enum CurrencyCode {
  """United Arab Emirates dirham"""
  AED

  """Afghan afghani"""
  AFN

  """Albanian lek"""
  ALL

  """Armenian dram"""
  AMD

  """Netherlands Antillean guilder"""
  ANG

  """Angolan kwanza"""
  AOA

  """Argentine peso"""
  ARS

  """Australian dollar"""
  AUD

  """Aruban florin"""
  AWG

  """Azerbaijani manat"""
  AZN

  """Bosnia and Herzegovina convertible mark"""
  BAM

  """Barbados dollar"""
  BBD

  """Bangladeshi taka"""
  BDT

  """Bulgarian lev"""
  BGN

  """Bahraini dinar"""
  BHD

  """Burundian franc"""
  BIF

  """Bermudian dollar"""
  BMD

  """Brunei dollar"""
  BND

  """Boliviano"""
  BOB

  """Brazilian real"""
  BRL

  """Bahamian dollar"""
  BSD

  """Bhutanese ngultrum"""
  BTN

  """Botswana pula"""
  BWP

  """Belarusian ruble"""
  BYN

  """Belize dollar"""
  BZD

  """Canadian dollar"""
  CAD

  """Congolese franc"""
  CDF

  """Swiss franc"""
  CHF

  """Chilean peso"""
  CLP

  """Renminbi (Chinese) yuan"""
  CNY

  """Colombian peso"""
  COP

  """Costa Rican colon"""
  CRC

  """Cuban convertible peso"""
  CUC

  """Cuban peso"""
  CUP

  """Cape Verde escudo"""
  CVE

  """Czech koruna"""
  CZK

  """Djiboutian franc"""
  DJF

  """Danish krone"""
  DKK

  """Dominican peso"""
  DOP

  """Algerian dinar"""
  DZD

  """Egyptian pound"""
  EGP

  """Eritrean nakfa"""
  ERN

  """Ethiopian birr"""
  ETB

  """Euro"""
  EUR

  """Fiji dollar"""
  FJD

  """Falkland Islands pound"""
  FKP

  """Pound sterling"""
  GBP

  """Georgian lari"""
  GEL

  """Ghanaian cedi"""
  GHS

  """Gibraltar pound"""
  GIP

  """Gambian dalasi"""
  GMD

  """Guinean franc"""
  GNF

  """Guatemalan quetzal"""
  GTQ

  """Guyanese dollar"""
  GYD

  """Hong Kong dollar"""
  HKD

  """Honduran lempira"""
  HNL

  """Croatian kuna"""
  HRK

  """Haitian gourde"""
  HTG

  """Hungarian forint"""
  HUF

  """Indonesian rupiah"""
  IDR

  """Israeli new shekel"""
  ILS

  """Indian rupee"""
  INR

  """Iraqi dinar"""
  IQD

  """Iranian rial"""
  IRR

  """Icelandic króna"""
  ISK

  """Jamaican dollar"""
  JMD

  """Jordanian dinar"""
  JOD

  """Japanese yen"""
  JPY

  """Kenyan shilling"""
  KES

  """Kyrgyzstani som"""
  KGS

  """Cambodian riel"""
  KHR

  """Comoro franc"""
  KMF

  """North Korean won"""
  KPW

  """South Korean won"""
  KRW

  """Kuwaiti dinar"""
  KWD

  """Cayman Islands dollar"""
  KYD

  """Kazakhstani tenge"""
  KZT

  """Lao kip"""
  LAK

  """Lebanese pound"""
  LBP

  """Sri Lankan rupee"""
  LKR

  """Liberian dollar"""
  LRD

  """Lesotho loti"""
  LSL

  """Libyan dinar"""
  LYD

  """Moroccan dirham"""
  MAD

  """Moldovan leu"""
  MDL

  """Malagasy ariary"""
  MGA

  """Macedonian denar"""
  MKD

  """Myanmar kyat"""
  MMK

  """Mongolian tögrög"""
  MNT

  """Macanese pataca"""
  MOP

  """Mauritanian ouguiya"""
  MRU

  """Mauritian rupee"""
  MUR

  """Maldivian rufiyaa"""
  MVR

  """Malawian kwacha"""
  MWK

  """Mexican peso"""
  MXN

  """Malaysian ringgit"""
  MYR

  """Mozambican metical"""
  MZN

  """Namibian dollar"""
  NAD

  """Nigerian naira"""
  NGN

  """Nicaraguan córdoba"""
  NIO

  """Norwegian krone"""
  NOK

  """Nepalese rupee"""
  NPR

  """New Zealand dollar"""
  NZD

  """Omani rial"""
  OMR

  """Panamanian balboa"""
  PAB

  """Peruvian sol"""
  PEN

  """Papua New Guinean kina"""
  PGK

  """Philippine peso"""
  PHP

  """Pakistani rupee"""
  PKR

  """Polish złoty"""
  PLN

  """Paraguayan guaraní"""
  PYG

  """Qatari riyal"""
  QAR

  """Romanian leu"""
  RON

  """Serbian dinar"""
  RSD

  """Russian ruble"""
  RUB

  """Rwandan franc"""
  RWF

  """Saudi riyal"""
  SAR

  """Solomon Islands dollar"""
  SBD

  """Seychelles rupee"""
  SCR

  """Sudanese pound"""
  SDG

  """Swedish krona/kronor"""
  SEK

  """Singapore dollar"""
  SGD

  """Saint Helena pound"""
  SHP

  """Sierra Leonean leone"""
  SLL

  """Somali shilling"""
  SOS

  """Surinamese dollar"""
  SRD

  """South Sudanese pound"""
  SSP

  """São Tomé and Príncipe dobra"""
  STN

  """Salvadoran colón"""
  SVC

  """Syrian pound"""
  SYP

  """Swazi lilangeni"""
  SZL

  """Thai baht"""
  THB

  """Tajikistani somoni"""
  TJS

  """Turkmenistan manat"""
  TMT

  """Tunisian dinar"""
  TND

  """Tongan paʻanga"""
  TOP

  """Turkish lira"""
  TRY

  """Trinidad and Tobago dollar"""
  TTD

  """New Taiwan dollar"""
  TWD

  """Tanzanian shilling"""
  TZS

  """Ukrainian hryvnia"""
  UAH

  """Ugandan shilling"""
  UGX

  """United States dollar"""
  USD

  """Uruguayan peso"""
  UYU

  """Uzbekistan som"""
  UZS

  """Venezuelan bolívar soberano"""
  VES

  """Vietnamese đồng"""
  VND

  """Vanuatu vatu"""
  VUV

  """Samoan tala"""
  WST

  """CFA franc BEAC"""
  XAF

  """East Caribbean dollar"""
  XCD

  """CFA franc BCEAO"""
  XOF

  """CFP franc (franc Pacifique)"""
  XPF

  """Yemeni rial"""
  YER

  """South African rand"""
  ZAR

  """Zambian kwacha"""
  ZMW

  """Zimbabwean dollar"""
  ZWL
}

type CurrentUser {
  channels: [CurrentUserChannel!]!
  id: ID!
  identifier: String!
}

type CurrentUserChannel {
  code: String!
  id: ID!
  permissions: [Permission!]!
  token: String!
}

interface CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

union CustomFieldConfig = BooleanCustomFieldConfig | DateTimeCustomFieldConfig | FloatCustomFieldConfig | IntCustomFieldConfig | LocaleStringCustomFieldConfig | LocaleTextCustomFieldConfig | RelationCustomFieldConfig | StringCustomFieldConfig | StructCustomFieldConfig | TextCustomFieldConfig

type Customer implements Node {
  addresses: [Address!]
  createdAt: DateTime!
  customFields: JSON
  emailAddress: String!
  firstName: String!
  id: ID!
  lastName: String!
  orders(options: OrderListOptions): OrderList!
  phoneNumber: String
  title: String
  updatedAt: DateTime!
  user: User
}

input CustomerFilterParameter {
  _and: [CustomerFilterParameter!]
  _or: [CustomerFilterParameter!]
  createdAt: DateOperators
  emailAddress: StringOperators
  firstName: StringOperators
  id: IDOperators
  lastName: StringOperators
  phoneNumber: StringOperators
  title: StringOperators
  updatedAt: DateOperators
}

type CustomerGroup implements Node {
  createdAt: DateTime!
  customFields: JSON
  customers(options: CustomerListOptions): CustomerList!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

type CustomerList implements PaginatedList {
  items: [Customer!]!
  totalItems: Int!
}

input CustomerListOptions {
  """Allows the results to be filtered"""
  filter: CustomerFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: CustomerSortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

input CustomerSortParameter {
  createdAt: SortOrder
  emailAddress: SortOrder
  firstName: SortOrder
  id: SortOrder
  lastName: SortOrder
  phoneNumber: SortOrder
  title: SortOrder
  updatedAt: SortOrder
}

"""Operators for filtering on a list of Date fields"""
input DateListOperators {
  inList: DateTime!
}

"""Operators for filtering on a DateTime field"""
input DateOperators {
  after: DateTime
  before: DateTime
  between: DateRange
  eq: DateTime
  isNull: Boolean
}

input DateRange {
  end: DateTime!
  start: DateTime!
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

"""
Expects the same validation formats as the `<input type="datetime-local">` HTML element.
See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/datetime-local#Additional_attributes
"""
type DateTimeCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  max: String
  min: String
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  step: Int
  type: String!
  ui: JSON
}

"""
Expects the same validation formats as the `<input type="datetime-local">` HTML element.
See https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/datetime-local#Additional_attributes
"""
type DateTimeStructFieldConfig implements StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  list: Boolean!
  max: String
  min: String
  name: String!
  step: Int
  type: String!
  ui: JSON
}

type DeletionResponse {
  message: String
  result: DeletionResult!
}

enum DeletionResult {
  """The entity was successfully deleted"""
  DELETED

  """Deletion did not take place, reason given in message"""
  NOT_DELETED
}

type Discount {
  adjustmentSource: String!
  amount: Money!
  amountWithTax: Money!
  description: String!
  type: AdjustmentType!
}

"""
Returned when attempting to create a Customer with an email address already registered to an existing User.
"""
type EmailAddressConflictError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

enum ErrorCode {
  ALREADY_LOGGED_IN_ERROR
  COUPON_CODE_EXPIRED_ERROR
  COUPON_CODE_INVALID_ERROR
  COUPON_CODE_LIMIT_ERROR
  EMAIL_ADDRESS_CONFLICT_ERROR
  GUEST_CHECKOUT_ERROR
  IDENTIFIER_CHANGE_TOKEN_EXPIRED_ERROR
  IDENTIFIER_CHANGE_TOKEN_INVALID_ERROR
  INELIGIBLE_PAYMENT_METHOD_ERROR
  INELIGIBLE_SHIPPING_METHOD_ERROR
  INSUFFICIENT_STOCK_ERROR
  INVALID_CREDENTIALS_ERROR
  MISSING_PASSWORD_ERROR
  NATIVE_AUTH_STRATEGY_ERROR
  NEGATIVE_QUANTITY_ERROR
  NOT_VERIFIED_ERROR
  NO_ACTIVE_ORDER_ERROR
  ORDER_INTERCEPTOR_ERROR
  ORDER_LIMIT_ERROR
  ORDER_MODIFICATION_ERROR
  ORDER_PAYMENT_STATE_ERROR
  ORDER_STATE_TRANSITION_ERROR
  PASSWORD_ALREADY_SET_ERROR
  PASSWORD_RESET_TOKEN_EXPIRED_ERROR
  PASSWORD_RESET_TOKEN_INVALID_ERROR
  PASSWORD_VALIDATION_ERROR
  PAYMENT_DECLINED_ERROR
  PAYMENT_FAILED_ERROR
  UNKNOWN_ERROR
  VERIFICATION_TOKEN_EXPIRED_ERROR
  VERIFICATION_TOKEN_INVALID_ERROR
}

interface ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

type Facet implements Node {
  code: String!
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  languageCode: LanguageCode!
  name: String!
  translations: [FacetTranslation!]!
  updatedAt: DateTime!

  """
  Returns a paginated, sortable, filterable list of the Facet's values. Added in v2.1.0.
  """
  valueList(options: FacetValueListOptions): FacetValueList!
  values: [FacetValue!]!
}

input FacetFilterParameter {
  _and: [FacetFilterParameter!]
  _or: [FacetFilterParameter!]
  code: StringOperators
  createdAt: DateOperators
  id: IDOperators
  languageCode: StringOperators
  name: StringOperators
  updatedAt: DateOperators
}

type FacetList implements PaginatedList {
  items: [Facet!]!
  totalItems: Int!
}

input FacetListOptions {
  """Allows the results to be filtered"""
  filter: FacetFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: FacetSortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

input FacetSortParameter {
  code: SortOrder
  createdAt: SortOrder
  id: SortOrder
  name: SortOrder
  updatedAt: SortOrder
}

type FacetTranslation {
  createdAt: DateTime!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

type FacetValue implements Node {
  code: String!
  createdAt: DateTime!
  customFields: JSON
  facet: Facet!
  facetId: ID!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  translations: [FacetValueTranslation!]!
  updatedAt: DateTime!
}

"""
Used to construct boolean expressions for filtering search results
by FacetValue ID. Examples:

* ID=1 OR ID=2: `{ facetValueFilters: [{ or: [1,2] }] }`
* ID=1 AND ID=2: `{ facetValueFilters: [{ and: 1 }, { and: 2 }] }`
* ID=1 AND (ID=2 OR ID=3): `{ facetValueFilters: [{ and: 1 }, { or: [2,3] }] }`
"""
input FacetValueFilterInput {
  and: ID
  or: [ID!]
}

input FacetValueFilterParameter {
  _and: [FacetValueFilterParameter!]
  _or: [FacetValueFilterParameter!]
  code: StringOperators
  createdAt: DateOperators
  facetId: IDOperators
  id: IDOperators
  languageCode: StringOperators
  name: StringOperators
  updatedAt: DateOperators
}

type FacetValueList implements PaginatedList {
  items: [FacetValue!]!
  totalItems: Int!
}

input FacetValueListOptions {
  """Allows the results to be filtered"""
  filter: FacetValueFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: FacetValueSortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

"""
Which FacetValues are present in the products returned
by the search, and in what quantity.
"""
type FacetValueResult {
  count: Int!
  facetValue: FacetValue!
}

input FacetValueSortParameter {
  code: SortOrder
  createdAt: SortOrder
  facetId: SortOrder
  id: SortOrder
  name: SortOrder
  updatedAt: SortOrder
}

type FacetValueTranslation {
  createdAt: DateTime!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

type FloatCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  max: Float
  min: Float
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  step: Float
  type: String!
  ui: JSON
}

type FloatStructFieldConfig implements StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  list: Boolean!
  max: Float
  min: Float
  name: String!
  step: Float
  type: String!
  ui: JSON
}

type Fulfillment implements Node {
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  lines: [FulfillmentLine!]!
  method: String!
  state: String!
  summary: [FulfillmentLine!]! @deprecated(reason: "Use the `lines` field instead")
  trackingCode: String
  updatedAt: DateTime!
}

type FulfillmentLine {
  fulfillment: Fulfillment!
  fulfillmentId: ID!
  orderLine: OrderLine!
  orderLineId: ID!
  quantity: Int!
}

enum GlobalFlag {
  FALSE
  INHERIT
  TRUE
}

"""
Returned when attempting to set the Customer on a guest checkout when the configured GuestCheckoutStrategy does not allow it.
"""
type GuestCheckoutError implements ErrorResult {
  errorCode: ErrorCode!
  errorDetail: String!
  message: String!
}

type HistoryEntry implements Node {
  createdAt: DateTime!
  customFields: JSON
  data: JSON!
  id: ID!
  type: HistoryEntryType!
  updatedAt: DateTime!
}

input HistoryEntryFilterParameter {
  _and: [HistoryEntryFilterParameter!]
  _or: [HistoryEntryFilterParameter!]
  createdAt: DateOperators
  id: IDOperators
  type: StringOperators
  updatedAt: DateOperators
}

type HistoryEntryList implements PaginatedList {
  items: [HistoryEntry!]!
  totalItems: Int!
}

input HistoryEntryListOptions {
  """Allows the results to be filtered"""
  filter: HistoryEntryFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: HistoryEntrySortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

input HistoryEntrySortParameter {
  createdAt: SortOrder
  id: SortOrder
  updatedAt: SortOrder
}

enum HistoryEntryType {
  CUSTOMER_ADDED_TO_GROUP
  CUSTOMER_ADDRESS_CREATED
  CUSTOMER_ADDRESS_DELETED
  CUSTOMER_ADDRESS_UPDATED
  CUSTOMER_DETAIL_UPDATED
  CUSTOMER_EMAIL_UPDATE_REQUESTED
  CUSTOMER_EMAIL_UPDATE_VERIFIED
  CUSTOMER_NOTE
  CUSTOMER_PASSWORD_RESET_REQUESTED
  CUSTOMER_PASSWORD_RESET_VERIFIED
  CUSTOMER_PASSWORD_UPDATED
  CUSTOMER_REGISTERED
  CUSTOMER_REMOVED_FROM_GROUP
  CUSTOMER_VERIFIED
  ORDER_CANCELLATION
  ORDER_COUPON_APPLIED
  ORDER_COUPON_REMOVED
  ORDER_CUSTOMER_UPDATED
  ORDER_FULFILLMENT
  ORDER_FULFILLMENT_TRANSITION
  ORDER_MODIFIED
  ORDER_NOTE
  ORDER_PAYMENT_TRANSITION
  ORDER_REFUND_TRANSITION
  ORDER_STATE_TRANSITION
}

"""Operators for filtering on a list of ID fields"""
input IDListOperators {
  inList: ID!
}

"""Operators for filtering on an ID field"""
input IDOperators {
  eq: String
  in: [String!]
  isNull: Boolean
  notEq: String
  notIn: [String!]
}

"""
Returned if the token used to change a Customer's email address is valid, but has
expired according to the `verificationTokenDuration` setting in the AuthOptions.
"""
type IdentifierChangeTokenExpiredError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned if the token used to change a Customer's email address is either
invalid or does not match any expected tokens.
"""
type IdentifierChangeTokenInvalidError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned when attempting to add a Payment using a PaymentMethod for which the Order is not eligible.
"""
type IneligiblePaymentMethodError implements ErrorResult {
  eligibilityCheckerMessage: String
  errorCode: ErrorCode!
  message: String!
}

"""
Returned when attempting to set a ShippingMethod for which the Order is not eligible
"""
type IneligibleShippingMethodError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned when attempting to add more items to the Order than are available
"""
type InsufficientStockError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
  order: Order!
  quantityAvailable: Int!
}

type IntCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  max: Int
  min: Int
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  step: Int
  type: String!
  ui: JSON
}

type IntStructFieldConfig implements StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  list: Boolean!
  max: Int
  min: Int
  name: String!
  step: Int
  type: String!
  ui: JSON
}

"""Returned if the user authentication credentials are not valid"""
type InvalidCredentialsError implements ErrorResult {
  authenticationError: String!
  errorCode: ErrorCode!
  message: String!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

"""
@description
Languages in the form of a ISO 639-1 language code with optional
region or script modifier (e.g. de_AT). The selection available is based
on the [Unicode CLDR summary list](https://unicode-org.github.io/cldr-staging/charts/37/summary/root.html)
and includes the major spoken languages of the world and any widely-used variants.

@docsCategory common
"""
enum LanguageCode {
  """Afrikaans"""
  af

  """Akan"""
  ak

  """Amharic"""
  am

  """Arabic"""
  ar

  """Assamese"""
  as

  """Azerbaijani"""
  az

  """Belarusian"""
  be

  """Bulgarian"""
  bg

  """Bambara"""
  bm

  """Bangla"""
  bn

  """Tibetan"""
  bo

  """Breton"""
  br

  """Bosnian"""
  bs

  """Catalan"""
  ca

  """Chechen"""
  ce

  """Corsican"""
  co

  """Czech"""
  cs

  """Church Slavic"""
  cu

  """Welsh"""
  cy

  """Danish"""
  da

  """German"""
  de

  """Austrian German"""
  de_AT

  """Swiss High German"""
  de_CH

  """Dzongkha"""
  dz

  """Ewe"""
  ee

  """Greek"""
  el

  """English"""
  en

  """Australian English"""
  en_AU

  """Canadian English"""
  en_CA

  """British English"""
  en_GB

  """American English"""
  en_US

  """Esperanto"""
  eo

  """Spanish"""
  es

  """European Spanish"""
  es_ES

  """Mexican Spanish"""
  es_MX

  """Estonian"""
  et

  """Basque"""
  eu

  """Persian"""
  fa

  """Dari"""
  fa_AF

  """Fulah"""
  ff

  """Finnish"""
  fi

  """Faroese"""
  fo

  """French"""
  fr

  """Canadian French"""
  fr_CA

  """Swiss French"""
  fr_CH

  """Western Frisian"""
  fy

  """Irish"""
  ga

  """Scottish Gaelic"""
  gd

  """Galician"""
  gl

  """Gujarati"""
  gu

  """Manx"""
  gv

  """Hausa"""
  ha

  """Hebrew"""
  he

  """Hindi"""
  hi

  """Croatian"""
  hr

  """Haitian Creole"""
  ht

  """Hungarian"""
  hu

  """Armenian"""
  hy

  """Interlingua"""
  ia

  """Indonesian"""
  id

  """Igbo"""
  ig

  """Sichuan Yi"""
  ii

  """Icelandic"""
  is

  """Italian"""
  it

  """Japanese"""
  ja

  """Javanese"""
  jv

  """Georgian"""
  ka

  """Kikuyu"""
  ki

  """Kazakh"""
  kk

  """Kalaallisut"""
  kl

  """Khmer"""
  km

  """Kannada"""
  kn

  """Korean"""
  ko

  """Kashmiri"""
  ks

  """Kurdish"""
  ku

  """Cornish"""
  kw

  """Kyrgyz"""
  ky

  """Latin"""
  la

  """Luxembourgish"""
  lb

  """Ganda"""
  lg

  """Lingala"""
  ln

  """Lao"""
  lo

  """Lithuanian"""
  lt

  """Luba-Katanga"""
  lu

  """Latvian"""
  lv

  """Malagasy"""
  mg

  """Maori"""
  mi

  """Macedonian"""
  mk

  """Malayalam"""
  ml

  """Mongolian"""
  mn

  """Marathi"""
  mr

  """Malay"""
  ms

  """Maltese"""
  mt

  """Burmese"""
  my

  """Norwegian Bokmål"""
  nb

  """North Ndebele"""
  nd

  """Nepali"""
  ne

  """Dutch"""
  nl

  """Flemish"""
  nl_BE

  """Norwegian Nynorsk"""
  nn

  """Nyanja"""
  ny

  """Oromo"""
  om

  """Odia"""
  or

  """Ossetic"""
  os

  """Punjabi"""
  pa

  """Polish"""
  pl

  """Pashto"""
  ps

  """Portuguese"""
  pt

  """Brazilian Portuguese"""
  pt_BR

  """European Portuguese"""
  pt_PT

  """Quechua"""
  qu

  """Romansh"""
  rm

  """Rundi"""
  rn

  """Romanian"""
  ro

  """Moldavian"""
  ro_MD

  """Russian"""
  ru

  """Kinyarwanda"""
  rw

  """Sanskrit"""
  sa

  """Sindhi"""
  sd

  """Northern Sami"""
  se

  """Sango"""
  sg

  """Sinhala"""
  si

  """Slovak"""
  sk

  """Slovenian"""
  sl

  """Samoan"""
  sm

  """Shona"""
  sn

  """Somali"""
  so

  """Albanian"""
  sq

  """Serbian"""
  sr

  """Southern Sotho"""
  st

  """Sundanese"""
  su

  """Swedish"""
  sv

  """Swahili"""
  sw

  """Congo Swahili"""
  sw_CD

  """Tamil"""
  ta

  """Telugu"""
  te

  """Tajik"""
  tg

  """Thai"""
  th

  """Tigrinya"""
  ti

  """Turkmen"""
  tk

  """Tongan"""
  to

  """Turkish"""
  tr

  """Tatar"""
  tt

  """Uyghur"""
  ug

  """Ukrainian"""
  uk

  """Urdu"""
  ur

  """Uzbek"""
  uz

  """Vietnamese"""
  vi

  """Volapük"""
  vo

  """Wolof"""
  wo

  """Xhosa"""
  xh

  """Yiddish"""
  yi

  """Yoruba"""
  yo

  """Chinese"""
  zh

  """Simplified Chinese"""
  zh_Hans

  """Traditional Chinese"""
  zh_Hant

  """Zulu"""
  zu
}

type LocaleStringCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  length: Int
  list: Boolean!
  name: String!
  nullable: Boolean
  pattern: String
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

type LocaleTextCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

type LocalizedString {
  languageCode: LanguageCode!
  value: String!
}

enum LogicalOperator {
  AND
  OR
}

"""
Returned when attempting to register or verify a customer account without a password, when one is required.
"""
type MissingPasswordError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
The `Money` scalar type represents monetary values and supports signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).
"""
scalar Money

type Mutation {
  """
  Adds an item to the Order. If custom fields are defined on the OrderLine entity, a third argument 'customFields' will be available.
  """
  addItemToOrder(productVariantId: ID!, quantity: Int!): UpdateOrderItemsResult!

  """Add a Payment to the Order"""
  addPaymentToOrder(input: PaymentInput!): AddPaymentToOrderResult!

  """
  Adjusts an OrderLine. If custom fields are defined on the OrderLine entity, a third argument 'customFields' of type `OrderLineCustomFieldsInput` will be available.
  """
  adjustOrderLine(orderLineId: ID!, quantity: Int!): UpdateOrderItemsResult!

  """Applies the given coupon code to the active Order"""
  applyCouponCode(couponCode: String!): ApplyCouponCodeResult!

  """Authenticates the user using a named authentication strategy"""
  authenticate(input: AuthenticationInput!, rememberMe: Boolean): AuthenticationResult!

  """Create a new Customer Address"""
  createCustomerAddress(input: CreateAddressInput!): Address!
  createStripePaymentIntent: String

  """Delete an existing Address"""
  deleteCustomerAddress(id: ID!): Success!

  """
  Authenticates the user using the native authentication strategy. This mutation is an alias for authenticate({ native: { ... }})
  
  The `rememberMe` option applies when using cookie-based sessions, and if `true` it will set the maxAge of the session cookie
  to 1 year.
  """
  login(password: String!, rememberMe: Boolean, username: String!): NativeAuthenticationResult!

  """End the current authenticated session"""
  logout: Success!

  """
  Regenerate and send a verification token for a new Customer registration. Only applicable if `authOptions.requireVerification` is set to true.
  """
  refreshCustomerVerification(emailAddress: String!): RefreshCustomerVerificationResult!

  """
  Register a Customer account with the given credentials. There are three possible registration flows:
  
  _If `authOptions.requireVerification` is set to `true`:_
  
  1. **The Customer is registered _with_ a password**. A verificationToken will be created (and typically emailed to the Customer). That
     verificationToken would then be passed to the `verifyCustomerAccount` mutation _without_ a password. The Customer is then
     verified and authenticated in one step.
  2. **The Customer is registered _without_ a password**. A verificationToken will be created (and typically emailed to the Customer). That
     verificationToken would then be passed to the `verifyCustomerAccount` mutation _with_ the chosen password of the Customer. The Customer is then
     verified and authenticated in one step.
  
  _If `authOptions.requireVerification` is set to `false`:_
  
  3. The Customer _must_ be registered _with_ a password. No further action is needed - the Customer is able to authenticate immediately.
  """
  registerCustomerAccount(input: RegisterCustomerInput!): RegisterCustomerAccountResult!

  """Remove all OrderLine from the Order"""
  removeAllOrderLines: RemoveOrderItemsResult!

  """Removes the given coupon code from the active Order"""
  removeCouponCode(couponCode: String!): Order

  """Remove an OrderLine from the Order"""
  removeOrderLine(orderLineId: ID!): RemoveOrderItemsResult!

  """Requests a password reset email to be sent"""
  requestPasswordReset(emailAddress: String!): RequestPasswordResetResult

  """
  Request to update the emailAddress of the active Customer. If `authOptions.requireVerification` is enabled
  (as is the default), then the `identifierChangeToken` will be assigned to the current User and
  a IdentifierChangeRequestEvent will be raised. This can then be used e.g. by the EmailPlugin to email
  that verification token to the Customer, which is then used to verify the change of email address.
  """
  requestUpdateCustomerEmailAddress(newEmailAddress: String!, password: String!): RequestUpdateCustomerEmailAddressResult!

  """Resets a Customer's password based on the provided token"""
  resetPassword(password: String!, token: String!): ResetPasswordResult!

  """
  Set the Customer for the Order. Required only if the Customer is not currently logged in
  """
  setCustomerForOrder(input: CreateCustomerInput!): SetCustomerForOrderResult!

  """Sets the billing address for the active Order"""
  setOrderBillingAddress(input: CreateAddressInput!): ActiveOrderResult!

  """Allows any custom fields to be set for the active Order"""
  setOrderCustomFields(input: UpdateOrderInput!): ActiveOrderResult!

  """Sets the shipping address for the active Order"""
  setOrderShippingAddress(input: CreateAddressInput!): ActiveOrderResult!

  """
  Sets the shipping method by id, which can be obtained with the `eligibleShippingMethods` query.
  An Order can have multiple shipping methods, in which case you can pass an array of ids. In this case,
  you should configure a custom ShippingLineAssignmentStrategy in order to know which OrderLines each
  shipping method will apply to.
  """
  setOrderShippingMethod(shippingMethodId: [ID!]!): SetOrderShippingMethodResult!

  """
  Transitions an Order to a new state. Valid next states can be found by querying `nextOrderStates`
  """
  transitionOrderToState(state: String!): TransitionOrderToStateResult

  """
  Unsets the billing address for the active Order. Available since version 3.1.0
  """
  unsetOrderBillingAddress: ActiveOrderResult!

  """
  Unsets the shipping address for the active Order. Available since version 3.1.0
  """
  unsetOrderShippingAddress: ActiveOrderResult!

  """Update an existing Customer"""
  updateCustomer(input: UpdateCustomerInput!): Customer!

  """Update an existing Address"""
  updateCustomerAddress(input: UpdateAddressInput!): Address!

  """
  Confirm the update of the emailAddress with the provided token, which has been generated by the
  `requestUpdateCustomerEmailAddress` mutation.
  """
  updateCustomerEmailAddress(token: String!): UpdateCustomerEmailAddressResult!

  """Update the password of the active Customer"""
  updateCustomerPassword(currentPassword: String!, newPassword: String!): UpdateCustomerPasswordResult!

  """
  Verify a Customer email address with the token sent to that address. Only applicable if `authOptions.requireVerification` is set to true.
  
  If the Customer was not registered with a password in the `registerCustomerAccount` mutation, the password _must_ be
  provided here.
  """
  verifyCustomerAccount(password: String, token: String!): VerifyCustomerAccountResult!
}

input NativeAuthInput {
  password: String!
  username: String!
}

"""
Returned when attempting an operation that relies on the NativeAuthStrategy, if that strategy is not configured.
"""
type NativeAuthStrategyError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

union NativeAuthenticationResult = CurrentUser | InvalidCredentialsError | NativeAuthStrategyError | NotVerifiedError

"""Returned when attempting to set a negative OrderLine quantity."""
type NegativeQuantityError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned when invoking a mutation which depends on there being an active Order on the
current session.
"""
type NoActiveOrderError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

interface Node {
  id: ID!
}

"""
Returned if `authOptions.requireVerification` is set to `true` (which is the default)
and an unverified user attempts to authenticate.
"""
type NotVerifiedError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""Operators for filtering on a list of Number fields"""
input NumberListOperators {
  inList: Float!
}

"""Operators for filtering on a Int or Float field"""
input NumberOperators {
  between: NumberRange
  eq: Float
  gt: Float
  gte: Float
  isNull: Boolean
  lt: Float
  lte: Float
}

input NumberRange {
  end: Float!
  start: Float!
}

type Order implements Node {
  """
  An order is active as long as the payment process has not been completed
  """
  active: Boolean!
  billingAddress: OrderAddress

  """A unique code for the Order"""
  code: String!

  """An array of all coupon codes applied to the Order"""
  couponCodes: [String!]!
  createdAt: DateTime!
  currencyCode: CurrencyCode!
  customFields: JSON
  customer: Customer
  discounts: [Discount!]!
  fulfillments: [Fulfillment!]
  history(options: HistoryEntryListOptions): HistoryEntryList!
  id: ID!
  lines: [OrderLine!]!

  """
  The date & time that the Order was placed, i.e. the Customer
  completed the checkout and the Order is no longer "active"
  """
  orderPlacedAt: DateTime
  payments: [Payment!]

  """
  Promotions applied to the order. Only gets populated after the payment process has completed.
  """
  promotions: [Promotion!]!
  shipping: Money!
  shippingAddress: OrderAddress
  shippingLines: [ShippingLine!]!
  shippingWithTax: Money!
  state: String!

  """
  The subTotal is the total of all OrderLines in the Order. This figure also includes any Order-level
  discounts which have been prorated (proportionally distributed) amongst the items of each OrderLine.
  To get a total of all OrderLines which does not account for prorated discounts, use the
  sum of `OrderLine.discountedLinePrice` values.
  """
  subTotal: Money!

  """Same as subTotal, but inclusive of tax"""
  subTotalWithTax: Money!

  """
  Surcharges are arbitrary modifications to the Order total which are neither
  ProductVariants nor discounts resulting from applied Promotions. For example,
  one-off discounts based on customer interaction, or surcharges based on payment
  methods.
  """
  surcharges: [Surcharge!]!

  """A summary of the taxes being applied to this Order"""
  taxSummary: [OrderTaxSummary!]!

  """Equal to subTotal plus shipping"""
  total: Money!
  totalQuantity: Int!

  """
  The final payable amount. Equal to subTotalWithTax plus shippingWithTax
  """
  totalWithTax: Money!
  type: OrderType!
  updatedAt: DateTime!
}

type OrderAddress {
  city: String
  company: String
  country: String
  countryCode: String
  customFields: JSON
  fullName: String
  phoneNumber: String
  postalCode: String
  province: String
  streetLine1: String
  streetLine2: String
}

input OrderFilterParameter {
  _and: [OrderFilterParameter!]
  _or: [OrderFilterParameter!]
  active: BooleanOperators
  code: StringOperators
  createdAt: DateOperators
  currencyCode: StringOperators
  id: IDOperators
  orderPlacedAt: DateOperators
  shipping: NumberOperators
  shippingWithTax: NumberOperators
  state: StringOperators
  subTotal: NumberOperators
  subTotalWithTax: NumberOperators
  total: NumberOperators
  totalQuantity: NumberOperators
  totalWithTax: NumberOperators
  type: StringOperators
  updatedAt: DateOperators
}

"""
Returned when an order operation is rejected by an OrderInterceptor method.
"""
type OrderInterceptorError implements ErrorResult {
  errorCode: ErrorCode!
  interceptorError: String!
  message: String!
}

"""Returned when the maximum order size limit has been reached."""
type OrderLimitError implements ErrorResult {
  errorCode: ErrorCode!
  maxItems: Int!
  message: String!
}

type OrderLine implements Node {
  createdAt: DateTime!
  customFields: JSON

  """The price of the line including discounts, excluding tax"""
  discountedLinePrice: Money!

  """The price of the line including discounts and tax"""
  discountedLinePriceWithTax: Money!

  """
  The price of a single unit including discounts, excluding tax.
  
  If Order-level discounts have been applied, this will not be the
  actual taxable unit price (see `proratedUnitPrice`), but is generally the
  correct price to display to customers to avoid confusion
  about the internal handling of distributed Order-level discounts.
  """
  discountedUnitPrice: Money!

  """The price of a single unit including discounts and tax"""
  discountedUnitPriceWithTax: Money!
  discounts: [Discount!]!
  featuredAsset: Asset
  fulfillmentLines: [FulfillmentLine!]
  id: ID!

  """The total price of the line excluding tax and discounts."""
  linePrice: Money!

  """The total price of the line including tax but excluding discounts."""
  linePriceWithTax: Money!

  """The total tax on this line"""
  lineTax: Money!
  order: Order!

  """The quantity at the time the Order was placed"""
  orderPlacedQuantity: Int!
  productVariant: ProductVariant!

  """
  The actual line price, taking into account both item discounts _and_ prorated (proportionally-distributed)
  Order-level discounts. This value is the true economic value of the OrderLine, and is used in tax
  and refund calculations.
  """
  proratedLinePrice: Money!

  """The proratedLinePrice including tax"""
  proratedLinePriceWithTax: Money!

  """
  The actual unit price, taking into account both item discounts _and_ prorated (proportionally-distributed)
  Order-level discounts. This value is the true economic value of the OrderItem, and is used in tax
  and refund calculations.
  """
  proratedUnitPrice: Money!

  """The proratedUnitPrice including tax"""
  proratedUnitPriceWithTax: Money!

  """The quantity of items purchased"""
  quantity: Int!
  taxLines: [TaxLine!]!
  taxRate: Float!

  """The price of a single unit, excluding tax and discounts"""
  unitPrice: Money!

  """
  Non-zero if the unitPrice has changed since it was initially added to Order
  """
  unitPriceChangeSinceAdded: Money!

  """The price of a single unit, including tax but excluding discounts"""
  unitPriceWithTax: Money!

  """
  Non-zero if the unitPriceWithTax has changed since it was initially added to Order
  """
  unitPriceWithTaxChangeSinceAdded: Money!
  updatedAt: DateTime!
}

type OrderList implements PaginatedList {
  items: [Order!]!
  totalItems: Int!
}

input OrderListOptions {
  """Allows the results to be filtered"""
  filter: OrderFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: OrderSortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

"""
Returned when attempting to modify the contents of an Order that is not in the `AddingItems` state.
"""
type OrderModificationError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned when attempting to add a Payment to an Order that is not in the `ArrangingPayment` state.
"""
type OrderPaymentStateError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

input OrderSortParameter {
  code: SortOrder
  createdAt: SortOrder
  id: SortOrder
  orderPlacedAt: SortOrder
  shipping: SortOrder
  shippingWithTax: SortOrder
  state: SortOrder
  subTotal: SortOrder
  subTotalWithTax: SortOrder
  total: SortOrder
  totalQuantity: SortOrder
  totalWithTax: SortOrder
  updatedAt: SortOrder
}

"""Returned if there is an error in transitioning the Order state"""
type OrderStateTransitionError implements ErrorResult {
  errorCode: ErrorCode!
  fromState: String!
  message: String!
  toState: String!
  transitionError: String!
}

"""
A summary of the taxes being applied to this order, grouped
by taxRate.
"""
type OrderTaxSummary {
  """A description of this tax"""
  description: String!

  """The total net price of OrderLines to which this taxRate applies"""
  taxBase: Money!

  """The taxRate as a percentage"""
  taxRate: Float!

  """The total tax being applied to the Order at this taxRate"""
  taxTotal: Money!
}

enum OrderType {
  Aggregate
  Regular
  Seller
}

interface PaginatedList {
  items: [Node!]!
  totalItems: Int!
}

"""
Returned when attempting to verify a customer account with a password, when a password has already been set.
"""
type PasswordAlreadySetError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned if the token used to reset a Customer's password is valid, but has
expired according to the `verificationTokenDuration` setting in the AuthOptions.
"""
type PasswordResetTokenExpiredError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned if the token used to reset a Customer's password is either
invalid or does not match any expected tokens.
"""
type PasswordResetTokenInvalidError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned when attempting to register or verify a customer account where the given password fails password validation.
"""
type PasswordValidationError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
  validationErrorMessage: String!
}

type Payment implements Node {
  amount: Money!
  createdAt: DateTime!
  customFields: JSON
  errorMessage: String
  id: ID!
  metadata: JSON
  method: String!
  refunds: [Refund!]!
  state: String!
  transactionId: String
  updatedAt: DateTime!
}

"""Returned when a Payment is declined by the payment provider."""
type PaymentDeclinedError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
  paymentErrorMessage: String!
}

"""Returned when a Payment fails due to an error."""
type PaymentFailedError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
  paymentErrorMessage: String!
}

"""Passed as input to the `addPaymentToOrder` mutation."""
input PaymentInput {
  """
  This field should contain arbitrary data passed to the specified PaymentMethodHandler's `createPayment()` method
  as the "metadata" argument. For example, it could contain an ID for the payment and other
  data generated by the payment provider.
  """
  metadata: JSON!

  """
  This field should correspond to the `code` property of a PaymentMethod.
  """
  method: String!
}

type PaymentMethod implements Node {
  checker: ConfigurableOperation
  code: String!
  createdAt: DateTime!
  customFields: JSON
  description: String!
  enabled: Boolean!
  handler: ConfigurableOperation!
  id: ID!
  name: String!
  translations: [PaymentMethodTranslation!]!
  updatedAt: DateTime!
}

type PaymentMethodQuote {
  code: String!
  customFields: JSON
  description: String!
  eligibilityMessage: String
  id: ID!
  isEligible: Boolean!
  name: String!
}

type PaymentMethodTranslation {
  createdAt: DateTime!
  description: String!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

"""
@description
Permissions for administrators and customers. Used to control access to
GraphQL resolvers via the {@link Allow} decorator.

## Understanding Permission.Owner

`Permission.Owner` is a special permission which is used in some Vendure resolvers to indicate that that resolver should only
be accessible to the "owner" of that resource.

For example, the Shop API `activeCustomer` query resolver should only return the Customer object for the "owner" of that Customer, i.e.
based on the activeUserId of the current session. As a result, the resolver code looks like this:

@example
```TypeScript
\@Query()
\@Allow(Permission.Owner)
async activeCustomer(\@Ctx() ctx: RequestContext): Promise<Customer | undefined> {
  const userId = ctx.activeUserId;
  if (userId) {
    return this.customerService.findOneByUserId(ctx, userId);
  }
}
```

Here we can see that the "ownership" must be enforced by custom logic inside the resolver. Since "ownership" cannot be defined generally
nor statically encoded at build-time, any resolvers using `Permission.Owner` **must** include logic to enforce that only the owner
of the resource has access. If not, then it is the equivalent of using `Permission.Public`.


@docsCategory common
"""
enum Permission {
  """Authenticated means simply that the user is logged in"""
  Authenticated

  """Grants permission to create Administrator"""
  CreateAdministrator

  """Grants permission to create Asset"""
  CreateAsset

  """Grants permission to create Products, Facets, Assets, Collections"""
  CreateCatalog

  """Grants permission to create Channel"""
  CreateChannel

  """Grants permission to create Collection"""
  CreateCollection

  """Grants permission to create Country"""
  CreateCountry

  """Grants permission to create Customer"""
  CreateCustomer

  """Grants permission to create CustomerGroup"""
  CreateCustomerGroup

  """Grants permission to create Facet"""
  CreateFacet

  """Grants permission to create Order"""
  CreateOrder

  """Grants permission to create PaymentMethod"""
  CreatePaymentMethod

  """Grants permission to create Product"""
  CreateProduct

  """Grants permission to create Promotion"""
  CreatePromotion

  """Grants permission to create Seller"""
  CreateSeller

  """
  Grants permission to create PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings
  """
  CreateSettings

  """Grants permission to create ShippingMethod"""
  CreateShippingMethod

  """Grants permission to create StockLocation"""
  CreateStockLocation

  """Grants permission to create System"""
  CreateSystem

  """Grants permission to create Tag"""
  CreateTag

  """Grants permission to create TaxCategory"""
  CreateTaxCategory

  """Grants permission to create TaxRate"""
  CreateTaxRate

  """Grants permission to create Zone"""
  CreateZone

  """Grants permission to delete Administrator"""
  DeleteAdministrator

  """Grants permission to delete Asset"""
  DeleteAsset

  """Grants permission to delete Products, Facets, Assets, Collections"""
  DeleteCatalog

  """Grants permission to delete Channel"""
  DeleteChannel

  """Grants permission to delete Collection"""
  DeleteCollection

  """Grants permission to delete Country"""
  DeleteCountry

  """Grants permission to delete Customer"""
  DeleteCustomer

  """Grants permission to delete CustomerGroup"""
  DeleteCustomerGroup

  """Grants permission to delete Facet"""
  DeleteFacet

  """Grants permission to delete Order"""
  DeleteOrder

  """Grants permission to delete PaymentMethod"""
  DeletePaymentMethod

  """Grants permission to delete Product"""
  DeleteProduct

  """Grants permission to delete Promotion"""
  DeletePromotion

  """Grants permission to delete Seller"""
  DeleteSeller

  """
  Grants permission to delete PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings
  """
  DeleteSettings

  """Grants permission to delete ShippingMethod"""
  DeleteShippingMethod

  """Grants permission to delete StockLocation"""
  DeleteStockLocation

  """Grants permission to delete System"""
  DeleteSystem

  """Grants permission to delete Tag"""
  DeleteTag

  """Grants permission to delete TaxCategory"""
  DeleteTaxCategory

  """Grants permission to delete TaxRate"""
  DeleteTaxRate

  """Grants permission to delete Zone"""
  DeleteZone

  """Owner means the user owns this entity, e.g. a Customer's own Order"""
  Owner

  """Public means any unauthenticated user may perform the operation"""
  Public

  """Grants permission to read Administrator"""
  ReadAdministrator

  """Grants permission to read Asset"""
  ReadAsset

  """Grants permission to read Products, Facets, Assets, Collections"""
  ReadCatalog

  """Grants permission to read Channel"""
  ReadChannel

  """Grants permission to read Collection"""
  ReadCollection

  """Grants permission to read Country"""
  ReadCountry

  """Grants permission to read Customer"""
  ReadCustomer

  """Grants permission to read CustomerGroup"""
  ReadCustomerGroup

  """Grants permission to read Facet"""
  ReadFacet

  """Grants permission to read Order"""
  ReadOrder

  """Grants permission to read PaymentMethod"""
  ReadPaymentMethod

  """Grants permission to read Product"""
  ReadProduct

  """Grants permission to read Promotion"""
  ReadPromotion

  """Grants permission to read Seller"""
  ReadSeller

  """
  Grants permission to read PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings
  """
  ReadSettings

  """Grants permission to read ShippingMethod"""
  ReadShippingMethod

  """Grants permission to read StockLocation"""
  ReadStockLocation

  """Grants permission to read System"""
  ReadSystem

  """Grants permission to read Tag"""
  ReadTag

  """Grants permission to read TaxCategory"""
  ReadTaxCategory

  """Grants permission to read TaxRate"""
  ReadTaxRate

  """Grants permission to read Zone"""
  ReadZone

  """SuperAdmin has unrestricted access to all operations"""
  SuperAdmin

  """Grants permission to update Administrator"""
  UpdateAdministrator

  """Grants permission to update Asset"""
  UpdateAsset

  """Grants permission to update Products, Facets, Assets, Collections"""
  UpdateCatalog

  """Grants permission to update Channel"""
  UpdateChannel

  """Grants permission to update Collection"""
  UpdateCollection

  """Grants permission to update Country"""
  UpdateCountry

  """Grants permission to update Customer"""
  UpdateCustomer

  """Grants permission to update CustomerGroup"""
  UpdateCustomerGroup

  """Grants permission to update Facet"""
  UpdateFacet

  """Grants permission to update GlobalSettings"""
  UpdateGlobalSettings

  """Grants permission to update Order"""
  UpdateOrder

  """Grants permission to update PaymentMethod"""
  UpdatePaymentMethod

  """Grants permission to update Product"""
  UpdateProduct

  """Grants permission to update Promotion"""
  UpdatePromotion

  """Grants permission to update Seller"""
  UpdateSeller

  """
  Grants permission to update PaymentMethods, ShippingMethods, TaxCategories, TaxRates, Zones, Countries, System & GlobalSettings
  """
  UpdateSettings

  """Grants permission to update ShippingMethod"""
  UpdateShippingMethod

  """Grants permission to update StockLocation"""
  UpdateStockLocation

  """Grants permission to update System"""
  UpdateSystem

  """Grants permission to update Tag"""
  UpdateTag

  """Grants permission to update TaxCategory"""
  UpdateTaxCategory

  """Grants permission to update TaxRate"""
  UpdateTaxRate

  """Grants permission to update Zone"""
  UpdateZone
}

"""The price range where the result has more than one price"""
type PriceRange {
  max: Money!
  min: Money!
}

type Product implements Node {
  assets: [Asset!]!
  collections: [Collection!]!
  createdAt: DateTime!
  customFields: JSON
  description: String!
  enabled: Boolean!
  facetValues: [FacetValue!]!
  featuredAsset: Asset
  id: ID!
  languageCode: LanguageCode!
  name: String!
  optionGroups: [ProductOptionGroup!]!
  slug: String!
  translations: [ProductTranslation!]!
  updatedAt: DateTime!

  """Returns a paginated, sortable, filterable list of ProductVariants"""
  variantList(options: ProductVariantListOptions): ProductVariantList!

  """Returns all ProductVariants"""
  variants: [ProductVariant!]!
}

input ProductFilterParameter {
  _and: [ProductFilterParameter!]
  _or: [ProductFilterParameter!]
  createdAt: DateOperators
  description: StringOperators
  enabled: BooleanOperators
  id: IDOperators
  languageCode: StringOperators
  name: StringOperators
  slug: StringOperators
  updatedAt: DateOperators
}

type ProductList implements PaginatedList {
  items: [Product!]!
  totalItems: Int!
}

input ProductListOptions {
  """Allows the results to be filtered"""
  filter: ProductFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: ProductSortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

type ProductOption implements Node {
  code: String!
  createdAt: DateTime!
  customFields: JSON
  group: ProductOptionGroup!
  groupId: ID!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  translations: [ProductOptionTranslation!]!
  updatedAt: DateTime!
}

type ProductOptionGroup implements Node {
  code: String!
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  languageCode: LanguageCode!
  name: String!
  options: [ProductOption!]!
  translations: [ProductOptionGroupTranslation!]!
  updatedAt: DateTime!
}

type ProductOptionGroupTranslation {
  createdAt: DateTime!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

type ProductOptionTranslation {
  createdAt: DateTime!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

input ProductSortParameter {
  createdAt: SortOrder
  description: SortOrder
  id: SortOrder
  name: SortOrder
  slug: SortOrder
  updatedAt: SortOrder
}

type ProductTranslation {
  createdAt: DateTime!
  description: String!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  slug: String!
  updatedAt: DateTime!
}

type ProductVariant implements Node {
  assets: [Asset!]!
  createdAt: DateTime!
  currencyCode: CurrencyCode!
  customFields: JSON
  facetValues: [FacetValue!]!
  featuredAsset: Asset
  id: ID!
  languageCode: LanguageCode!
  name: String!
  options: [ProductOption!]!
  price: Money!
  priceWithTax: Money!
  product: Product!
  productId: ID!
  sku: String!
  stockLevel: String!
  taxCategory: TaxCategory!
  taxRateApplied: TaxRate!
  translations: [ProductVariantTranslation!]!
  updatedAt: DateTime!
}

input ProductVariantFilterParameter {
  _and: [ProductVariantFilterParameter!]
  _or: [ProductVariantFilterParameter!]
  createdAt: DateOperators
  currencyCode: StringOperators
  id: IDOperators
  languageCode: StringOperators
  name: StringOperators
  price: NumberOperators
  priceWithTax: NumberOperators
  productId: IDOperators
  sku: StringOperators
  stockLevel: StringOperators
  updatedAt: DateOperators
}

type ProductVariantList implements PaginatedList {
  items: [ProductVariant!]!
  totalItems: Int!
}

input ProductVariantListOptions {
  """Allows the results to be filtered"""
  filter: ProductVariantFilterParameter

  """
  Specifies whether multiple top-level "filter" fields should be combined with a logical AND or OR operation. Defaults to AND.
  """
  filterOperator: LogicalOperator

  """Skips the first n results, for use in pagination"""
  skip: Int

  """Specifies which properties to sort the results by"""
  sort: ProductVariantSortParameter

  """Takes n results, for use in pagination"""
  take: Int
}

input ProductVariantSortParameter {
  createdAt: SortOrder
  id: SortOrder
  name: SortOrder
  price: SortOrder
  priceWithTax: SortOrder
  productId: SortOrder
  sku: SortOrder
  stockLevel: SortOrder
  updatedAt: SortOrder
}

type ProductVariantTranslation {
  createdAt: DateTime!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

type Promotion implements Node {
  actions: [ConfigurableOperation!]!
  conditions: [ConfigurableOperation!]!
  couponCode: String
  createdAt: DateTime!
  customFields: JSON
  description: String!
  enabled: Boolean!
  endsAt: DateTime
  id: ID!
  name: String!
  perCustomerUsageLimit: Int
  startsAt: DateTime
  translations: [PromotionTranslation!]!
  updatedAt: DateTime!
  usageLimit: Int
}

type PromotionList implements PaginatedList {
  items: [Promotion!]!
  totalItems: Int!
}

type PromotionTranslation {
  createdAt: DateTime!
  description: String!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

type Province implements Node & Region {
  code: String!
  createdAt: DateTime!
  customFields: JSON
  enabled: Boolean!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  parent: Region
  parentId: ID
  translations: [RegionTranslation!]!
  type: String!
  updatedAt: DateTime!
}

type ProvinceList implements PaginatedList {
  items: [Province!]!
  totalItems: Int!
}

type PublicPaymentMethod {
  code: String!
  description: String
  id: ID!
  name: String!
  translations: [PaymentMethodTranslation!]!
}

type PublicShippingMethod {
  code: String!
  description: String
  id: ID!
  name: String!
  translations: [ShippingMethodTranslation!]!
}

type Query {
  """The active Channel"""
  activeChannel: Channel!

  """The active Customer"""
  activeCustomer: Customer

  """
  The active Order. Will be `null` until an Order is created via `addItemToOrder`. Once an Order reaches the
  state of `PaymentAuthorized` or `PaymentSettled`, then that Order is no longer considered "active" and this
  query will once again return `null`.
  """
  activeOrder: Order

  """Get active payment methods"""
  activePaymentMethods: [PublicPaymentMethod]!

  """Get active shipping methods"""
  activeShippingMethods: [PublicShippingMethod]!

  """An array of supported Countries"""
  availableCountries: [Country!]!

  """
  Returns a Collection either by its id or slug. If neither 'id' nor 'slug' is specified, an error will result.
  """
  collection(id: ID, slug: String): Collection

  """A list of Collections available to the shop"""
  collections(options: CollectionListOptions): CollectionList!

  """
  Returns a list of payment methods and their eligibility based on the current active Order
  """
  eligiblePaymentMethods: [PaymentMethodQuote!]!

  """
  Returns a list of eligible shipping methods based on the current active Order
  """
  eligibleShippingMethods: [ShippingMethodQuote!]!

  """Returns a Facet by its id"""
  facet(id: ID!): Facet

  """A list of Facets available to the shop"""
  facets(options: FacetListOptions): FacetList!
  generateBraintreeClientToken(includeCustomerId: Boolean, orderId: ID): String

  """Returns information about the current authenticated User"""
  me: CurrentUser

  """
  Returns the possible next states that the activeOrder can transition to
  """
  nextOrderStates: [String!]!

  """
  Returns an Order based on the id. Note that in the Shop API, only orders belonging to the
  currently-authenticated User may be queried.
  """
  order(id: ID!): Order

  """
  Returns an Order based on the order `code`. For guest Orders (i.e. Orders placed by non-authenticated Customers)
  this query will only return the Order within 2 hours of the Order being placed. This allows an Order confirmation
  screen to be shown immediately after completion of a guest checkout, yet prevents security risks of allowing
  general anonymous access to Order data.
  """
  orderByCode(code: String!): Order

  """
  Get a Product either by id or slug. If neither 'id' nor 'slug' is specified, an error will result.
  """
  product(id: ID, slug: String): Product

  """Get a list of Products"""
  products(options: ProductListOptions): ProductList!

  """Search Products based on the criteria set by the `SearchInput`"""
  search(input: SearchInput!): SearchResponse!
}

union RefreshCustomerVerificationResult = NativeAuthStrategyError | Success

type Refund implements Node {
  adjustment: Money!
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  items: Money!
  lines: [RefundLine!]!
  metadata: JSON
  method: String
  paymentId: ID!
  reason: String
  shipping: Money!
  state: String!
  total: Money!
  transactionId: String
  updatedAt: DateTime!
}

type RefundLine {
  orderLine: OrderLine!
  orderLineId: ID!
  quantity: Int!
  refund: Refund!
  refundId: ID!
}

interface Region implements Node {
  code: String!
  createdAt: DateTime!
  enabled: Boolean!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  parent: Region
  parentId: ID
  translations: [RegionTranslation!]!
  type: String!
  updatedAt: DateTime!
}

type RegionTranslation {
  createdAt: DateTime!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

union RegisterCustomerAccountResult = MissingPasswordError | NativeAuthStrategyError | PasswordValidationError | Success

input RegisterCustomerInput {
  emailAddress: String!
  firstName: String
  lastName: String
  password: String
  phoneNumber: String
  title: String
}

type RelationCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  entity: String!
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  scalarFields: [String!]!
  type: String!
  ui: JSON
}

union RemoveOrderItemsResult = Order | OrderInterceptorError | OrderModificationError

union RequestPasswordResetResult = NativeAuthStrategyError | Success

union RequestUpdateCustomerEmailAddressResult = EmailAddressConflictError | InvalidCredentialsError | NativeAuthStrategyError | Success

union ResetPasswordResult = CurrentUser | NativeAuthStrategyError | NotVerifiedError | PasswordResetTokenExpiredError | PasswordResetTokenInvalidError | PasswordValidationError

type Role implements Node {
  channels: [Channel!]!
  code: String!
  createdAt: DateTime!
  description: String!
  id: ID!
  permissions: [Permission!]!
  updatedAt: DateTime!
}

type RoleList implements PaginatedList {
  items: [Role!]!
  totalItems: Int!
}

input SearchInput {
  collectionId: ID
  collectionSlug: String
  facetValueFilters: [FacetValueFilterInput!]
  groupByProduct: Boolean
  inStock: Boolean
  skip: Int
  sort: SearchResultSortParameter
  take: Int
  term: String
}

type SearchReindexResponse {
  success: Boolean!
}

type SearchResponse {
  collections: [CollectionResult!]!
  facetValues: [FacetValueResult!]!
  items: [SearchResult!]!
  totalItems: Int!
}

type SearchResult {
  """An array of ids of the Collections in which this result appears"""
  collectionIds: [ID!]!
  currencyCode: CurrencyCode!
  description: String!
  facetIds: [ID!]!
  facetValueIds: [ID!]!
  inStock: Boolean!
  price: SearchResultPrice!
  priceWithTax: SearchResultPrice!
  productAsset: SearchResultAsset
  productId: ID!
  productName: String!
  productVariantAsset: SearchResultAsset
  productVariantId: ID!
  productVariantName: String!

  """
  A relevance score for the result. Differs between database implementations
  """
  score: Float!
  sku: String!
  slug: String!
}

type SearchResultAsset {
  focalPoint: Coordinate
  id: ID!
  preview: String!
}

"""
The price of a search result product, either as a range or as a single price
"""
union SearchResultPrice = PriceRange | SinglePrice

input SearchResultSortParameter {
  name: SortOrder
  price: SortOrder
}

type Seller implements Node {
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  name: String!
  updatedAt: DateTime!
}

union SetCustomerForOrderResult = AlreadyLoggedInError | EmailAddressConflictError | GuestCheckoutError | NoActiveOrderError | Order

union SetOrderShippingMethodResult = IneligibleShippingMethodError | NoActiveOrderError | Order | OrderModificationError

type ShippingLine {
  customFields: JSON
  discountedPrice: Money!
  discountedPriceWithTax: Money!
  discounts: [Discount!]!
  id: ID!
  price: Money!
  priceWithTax: Money!
  shippingMethod: ShippingMethod!
}

type ShippingMethod implements Node {
  calculator: ConfigurableOperation!
  checker: ConfigurableOperation!
  code: String!
  createdAt: DateTime!
  customFields: JSON
  description: String!
  fulfillmentHandlerCode: String!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  translations: [ShippingMethodTranslation!]!
  updatedAt: DateTime!
}

type ShippingMethodList implements PaginatedList {
  items: [ShippingMethod!]!
  totalItems: Int!
}

type ShippingMethodQuote {
  code: String!
  customFields: JSON
  description: String!
  id: ID!

  """
  Any optional metadata returned by the ShippingCalculator in the ShippingCalculationResult
  """
  metadata: JSON
  name: String!
  price: Money!
  priceWithTax: Money!
}

type ShippingMethodTranslation {
  createdAt: DateTime!
  description: String!
  id: ID!
  languageCode: LanguageCode!
  name: String!
  updatedAt: DateTime!
}

"""The price value where the result has a single price"""
type SinglePrice {
  value: Money!
}

enum SortOrder {
  ASC
  DESC
}

type StringCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  length: Int
  list: Boolean!
  name: String!
  nullable: Boolean
  options: [StringFieldOption!]
  pattern: String
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

type StringFieldOption {
  label: [LocalizedString!]
  value: String!
}

"""Operators for filtering on a list of String fields"""
input StringListOperators {
  inList: String!
}

"""Operators for filtering on a String field"""
input StringOperators {
  contains: String
  eq: String
  in: [String!]
  isNull: Boolean
  notContains: String
  notEq: String
  notIn: [String!]
  regex: String
}

type StringStructFieldConfig implements StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  length: Int
  list: Boolean!
  name: String!
  options: [StringFieldOption!]
  pattern: String
  type: String!
  ui: JSON
}

type StructCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  fields: [StructFieldConfig!]!
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

interface StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  list: Boolean
  name: String!
  type: String!
  ui: JSON
}

union StructFieldConfig = BooleanStructFieldConfig | DateTimeStructFieldConfig | FloatStructFieldConfig | IntStructFieldConfig | StringStructFieldConfig | TextStructFieldConfig

"""
Indicates that an operation succeeded, where we do not want to return any more specific information.
"""
type Success {
  success: Boolean!
}

type Surcharge implements Node {
  createdAt: DateTime!
  description: String!
  id: ID!
  price: Money!
  priceWithTax: Money!
  sku: String
  taxLines: [TaxLine!]!
  taxRate: Float!
  updatedAt: DateTime!
}

type Tag implements Node {
  createdAt: DateTime!
  id: ID!
  updatedAt: DateTime!
  value: String!
}

type TagList implements PaginatedList {
  items: [Tag!]!
  totalItems: Int!
}

type TaxCategory implements Node {
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  isDefault: Boolean!
  name: String!
  updatedAt: DateTime!
}

type TaxLine {
  description: String!
  taxRate: Float!
}

type TaxRate implements Node {
  category: TaxCategory!
  createdAt: DateTime!
  customFields: JSON
  customerGroup: CustomerGroup
  enabled: Boolean!
  id: ID!
  name: String!
  updatedAt: DateTime!
  value: Float!
  zone: Zone!
}

type TaxRateList implements PaginatedList {
  items: [TaxRate!]!
  totalItems: Int!
}

type TextCustomFieldConfig implements CustomField {
  description: [LocalizedString!]
  internal: Boolean
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  nullable: Boolean
  readonly: Boolean
  requiresPermission: [Permission!]
  type: String!
  ui: JSON
}

type TextStructFieldConfig implements StructField {
  description: [LocalizedString!]
  label: [LocalizedString!]
  list: Boolean!
  name: String!
  type: String!
  ui: JSON
}

union TransitionOrderToStateResult = Order | OrderStateTransitionError

"""
Input used to update an Address.

The countryCode must correspond to a `code` property of a Country that has been defined in the
Vendure server. The `code` property is typically a 2-character ISO code such as "GB", "US", "DE" etc.
If an invalid code is passed, the mutation will fail.
"""
input UpdateAddressInput {
  city: String
  company: String
  countryCode: String
  customFields: JSON
  defaultBillingAddress: Boolean
  defaultShippingAddress: Boolean
  fullName: String
  id: ID!
  phoneNumber: String
  postalCode: String
  province: String
  streetLine1: String
  streetLine2: String
}

union UpdateCustomerEmailAddressResult = IdentifierChangeTokenExpiredError | IdentifierChangeTokenInvalidError | NativeAuthStrategyError | Success

input UpdateCustomerInput {
  customFields: JSON
  firstName: String
  lastName: String
  phoneNumber: String
  title: String
}

union UpdateCustomerPasswordResult = InvalidCredentialsError | NativeAuthStrategyError | PasswordValidationError | Success

input UpdateOrderInput {
  customFields: JSON
}

union UpdateOrderItemsResult = InsufficientStockError | NegativeQuantityError | Order | OrderInterceptorError | OrderLimitError | OrderModificationError

"""The `Upload` scalar type represents a file upload."""
scalar Upload

type User implements Node {
  authenticationMethods: [AuthenticationMethod!]!
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  identifier: String!
  lastLogin: DateTime
  roles: [Role!]!
  updatedAt: DateTime!
  verified: Boolean!
}

"""
Returned if the verification token (used to verify a Customer's email address) is valid, but has
expired according to the `verificationTokenDuration` setting in the AuthOptions.
"""
type VerificationTokenExpiredError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

"""
Returned if the verification token (used to verify a Customer's email address) is either
invalid or does not match any expected tokens.
"""
type VerificationTokenInvalidError implements ErrorResult {
  errorCode: ErrorCode!
  message: String!
}

union VerifyCustomerAccountResult = CurrentUser | MissingPasswordError | NativeAuthStrategyError | PasswordAlreadySetError | PasswordValidationError | VerificationTokenExpiredError | VerificationTokenInvalidError

type Zone implements Node {
  createdAt: DateTime!
  customFields: JSON
  id: ID!
  members: [Region!]!
  name: String!
  updatedAt: DateTime!
}