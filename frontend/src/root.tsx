import { component$, useStyles$, useVisibleTask$ } from '@qwik.dev/core';
import { QwikRouterProvider, RouterOutlet, ServiceWorkerRegister } from '@qwik.dev/router';
import { Head } from './components/head/head';

import globalStyles from './global.css?inline';

// Import GraphQL interceptor to enable automatic reCAPTCHA token injection
import './utils/graphql-interceptor';

export default component$(() => {
	useStyles$(globalStyles);

	// This single task handles both production log silencing and DOM cleanup.
	useVisibleTask$(() => {
		// 🔇 PRODUCTION: Silence console logs in production to improve performance and hide debug info
		if (import.meta.env.PROD) {
			console.log = () => {};
			console.warn = () => {};
			console.info = () => {};
			console.debug = () => {};
			// Keep console.error for critical issues
		}

		// 🚀 PERFORMANCE FIX: Prevent empty modulepreload links and aggressive bundle graph preloading
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				mutation.addedNodes.forEach((node) => {
					if (node instanceof HTMLLinkElement) {
						// More aggressive check for problematic modulepreload links
						if (node.rel === 'modulepreload') {
							// Remove if href is missing, empty, or invalid
							if (!node.href || node.href.trim() === '' || !node.hasAttribute('href') || node.href === window.location.href) {
								node.remove();
								return;
							}
						}
						// Remove bundle graph preloads that cause warnings
						if (node.rel === 'preload' && node.href && node.href.includes('bundle-graph')) {
							node.remove();
						}
					}
				});
			});
		});

		observer.observe(document.head, { childList: true, subtree: true });

		// More aggressive cleanup function
		const cleanupExistingLinks = () => {
			const problematicLinks = document.querySelectorAll(
				'link[rel="modulepreload"]:not([href]), link[rel="modulepreload"][href=""], link[rel="modulepreload"][href="' + window.location.href + '"], link[rel="preload"][href*="bundle-graph"]'
			);
			problematicLinks.forEach(link => link.remove());
		};
		
		cleanupExistingLinks();
		// Multiple cleanup attempts to catch Qwik's aggressive preloading
		setTimeout(cleanupExistingLinks, 50);
		setTimeout(cleanupExistingLinks, 200);
		setTimeout(cleanupExistingLinks, 500);

		return () => observer.disconnect();
	});

	// Handle iOS-specific compatibility issues
	useVisibleTask$(() => {
		const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
		if (!isIOS) return;

		// Fix viewport height issues on iOS
		const setVH = () => {
			document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
		};
		setVH();

		// Prevent zoom on input focus
		const preventZoom = (e: Event) => {
			const target = e.target as HTMLElement;
			if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
				target.style.fontSize = '16px';
			}
		};

		window.addEventListener('resize', setVH);
		window.addEventListener('orientationchange', setVH);
		document.addEventListener('focusin', preventZoom);

		return () => {
			window.removeEventListener('resize', setVH);
			window.removeEventListener('orientationchange', setVH);
			document.removeEventListener('focusin', preventZoom);
		};
	});

	return (
		<QwikRouterProvider>
			<Head />
			<body lang="en">
				<RouterOutlet />
				<ServiceWorkerRegister />
			</body>
		</QwikRouterProvider>
	);
});
