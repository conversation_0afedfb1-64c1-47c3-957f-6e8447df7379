import { $, component$, useContext, useSignal, useVisibleTask$ } from '@qwik.dev/core';
import { ErrorMessage } from '~/components/error-message/ErrorMessage';
import ShieldCheckIcon from '~/components/icons/ShieldCheckIcon';
import LogoutIcon from '~/components/icons/LogoutIcon';
import ShoppingBagIcon from '~/components/icons/ShoppingBagIcon';
import HeartIcon from '~/components/icons/HeartIcon';
import ChevronRightIcon from '~/components/icons/ChevronRightIcon';
import CheckIcon from '~/components/icons/CheckIcon';
import { Modal } from '~/components/modal/Modal';
import { HighlightedButton } from '~/components/buttons/HighlightedButton';
import { APP_STATE } from '~/constants';
import { requestUpdateCustomerEmailAddressMutation, updateCustomerMutation } from '~/providers/shop/account/account';
import { getActiveCustomerQuery, logoutMutation } from '~/providers/shop/customer/customer';
import { createSEOHead } from '~/utils/seo';
import PencilSquareIcon from '~/components/icons/PencilSquareIcon';
import { ActiveCustomer } from '~/types';

export default component$(() => {
	const appState = useContext(APP_STATE);
	const newEmail = useSignal('');
	const errorMessage = useSignal('');
	const currentPassword = useSignal('');
	const showModal = useSignal(false);
	const isEditing = useSignal(false);
	const update = useSignal<Partial<ActiveCustomer>>({});
	const profileFeedback = useSignal<string | null>(null);

	useVisibleTask$(async () => {
		const activeCustomer = await getActiveCustomerQuery();
		appState.customer = {
			title: activeCustomer.title ?? '',
			firstName: activeCustomer.firstName,
			id: activeCustomer.id,
			lastName: activeCustomer.lastName,
			emailAddress: activeCustomer.emailAddress,
			phoneNumber: activeCustomer.phoneNumber ?? '',
		};
		newEmail.value = activeCustomer?.emailAddress as string;
	});

	const updateEmail = $(async (password: string, newEmail: string) => {
		const { requestUpdateCustomerEmailAddress } = await requestUpdateCustomerEmailAddressMutation(
			password,
			newEmail
		);
		if (requestUpdateCustomerEmailAddress.__typename === 'InvalidCredentialsError') {
			errorMessage.value = requestUpdateCustomerEmailAddress.message || '';
		} else {
			errorMessage.value = '';
			showModal.value = false;
		}
	});

	const updateCustomer = $(async () => {
		profileFeedback.value = null;
		try {
			// Only pass fields that are allowed in UpdateCustomerInput
			const updateInput = {
				title: (update.value.title !== undefined ? update.value.title : appState.customer?.title) || undefined,
				firstName: (update.value.firstName !== undefined ? update.value.firstName : appState.customer?.firstName) || undefined,
				lastName: (update.value.lastName !== undefined ? update.value.lastName : appState.customer?.lastName) || undefined,
				phoneNumber: (update.value.phoneNumber !== undefined ? update.value.phoneNumber : appState.customer?.phoneNumber) || undefined,
			};
			
			const result = await updateCustomerMutation(updateInput);
			if (result.updateCustomer) {
				// Update the local state with the new values
				appState.customer = { ...appState.customer, ...update.value };
				profileFeedback.value = 'Profile updated successfully!';
				if (appState.customer.emailAddress !== newEmail.value) {
					showModal.value = true;
				} else {
					isEditing.value = false;
				}
			}
		} catch (err: any) {
			profileFeedback.value = err?.message || 'Failed to update profile.';
		}
	});

	const quickActions = [
		{
			title: 'Orders',
			description: 'View and track your orders',
			icon: ShoppingBagIcon,
			href: '/account/orders',
			color: 'bg-gradient-to-br from-red-100 to-red-200 hover:from-red-200 hover:to-red-300',
			iconColor: 'text-red-500',
			count: 'History',
		},
		{
			title: 'Addresses',
			description: 'Manage shipping & billing addresses',
			icon: PencilSquareIcon,
			href: '/account/address-book',
			color: 'bg-gradient-to-br from-blue-100 to-blue-200 hover:from-blue-200 hover:to-blue-300',
			iconColor: 'text-blue-600',
			count: 'Manage',
		},
		{
			title: 'Password',
			description: 'Change your account password',
			icon: ShieldCheckIcon,
			href: '/account/password',
			color: 'bg-gradient-to-br from-green-100 to-green-200 hover:from-green-200 hover:to-green-300',
			iconColor: 'text-green-600',
			count: 'Security',
		},
		{
			title: 'Support',
			description: 'Get help with your account',
			icon: HeartIcon,
			href: '/contact',
			color: 'bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200',
			iconColor: 'text-purple-600',
			count: 'Available',
		},
	];

	return (
		<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
			{/* Hero Section */}
			<div class="relative overflow-hidden bg-gradient-to-r from-gray-900 via-black to-gray-900">
				<div class="absolute inset-0 bg-gradient-to-r from-red-600/10 to-red-800/10"></div>
				<div class="relative max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-20 sm:py-24 lg:py-32">
					<div class="text-center">
						<h1 class="text-3xl sm:text-4xl md:text-6xl font-bold text-white mb-4 font-heading leading-tight">
							Welcome Back,
						</h1>
						<h2 class="text-xl sm:text-2xl md:text-4xl text-red-400 font-heading mb-6 px-4">
							{appState.customer?.title && (
								<span class="text-lg sm:text-xl font-normal mr-2">{appState.customer?.title}</span>
							)}
							{appState.customer?.firstName} {appState.customer?.lastName}
						</h2>
						<p class="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto px-4 leading-relaxed">
							Manage your account, track orders, and explore the darkly beautiful world of Damned Designs
						</p>
						{/* Logout Button */}
						<button
							class="absolute top-4 right-4 sm:top-6 sm:right-6 flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200 px-3 py-2 sm:px-4 sm:py-2 rounded-lg hover:bg-white/10 cursor-pointer"
							onClick$={async () => {
								await logoutMutation();
								window.location.href = '/';
							}}
						>
							<LogoutIcon />
							<span class="hidden sm:inline">Logout</span>
						</button>
					</div>
				</div>
			</div>

			{/* Profile Card & Quick Actions */}
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-16 sm:-mt-20 lg:-mt-24 relative z-10 pb-16 overflow-hidden">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
					{/* Profile Card */}
					<div class="bg-white rounded-2xl shadow-xl p-6 sm:p-8 flex flex-col items-center border border-gray-100">
						<div class="relative">
							<div class="h-24 w-24 sm:h-28 sm:w-28 rounded-full bg-gradient-to-br from-red-400 to-red-600 flex items-center justify-center text-white text-2xl sm:text-4xl font-bold border-4 border-white shadow-lg">
								{appState.customer?.firstName?.[0]}{appState.customer?.lastName?.[0]}
							</div>
						</div>
						<div class="mt-6 text-center">
							<h3 class="text-xl sm:text-2xl font-bold text-gray-900 font-heading">
								{appState.customer?.firstName} {appState.customer?.lastName}
							</h3>
							<p class="text-gray-500 text-base sm:text-lg break-all">{appState.customer?.emailAddress}</p>
							{appState.customer?.phoneNumber && (
								<p class="text-gray-500 text-sm sm:text-base mt-1">{appState.customer?.phoneNumber}</p>
							)}
						</div>
						<div class="mt-6">
							<button
								onClick$={() => (isEditing.value = true)}
								class="flex items-center space-x-2 text-[#e34545] hover:text-black transition-colors duration-200 px-4 py-2 rounded-lg hover:bg-red-50 cursor-pointer"
							>
								<PencilSquareIcon />
								<span class="font-medium">Edit Profile</span>
							</button>
						</div>
					</div>

					{/* Quick Actions */}
					<div class="lg:col-span-2 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
						{quickActions.map((action) => (
							<a
								key={action.title}
								href={action.href}
								class={`rounded-xl sm:rounded-2xl p-3 sm:p-4 lg:p-6 flex flex-col items-center justify-center shadow border border-gray-100 transition-all duration-200 group cursor-pointer min-w-0 h-32 sm:h-36 lg:h-40 ${action.color}`}
							>
								<div class={`w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 flex items-center justify-center rounded-full mb-2 bg-white shadow flex-shrink-0 ${action.iconColor}`}>
									<action.icon />
								</div>
								<div class="text-xs sm:text-sm lg:text-base font-semibold text-gray-900 mb-1 text-center leading-tight">{action.title}</div>
								<div class="text-gray-500 text-xs leading-tight mb-1 text-center px-1 overflow-hidden"
									style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">{action.description}</div>
								<div class="flex items-center gap-1 text-xs text-gray-400 group-hover:text-black flex-shrink-0 mt-auto">
									{action.count} <ChevronRightIcon />
								</div>
							</a>
						))}
					</div>
				</div>

				{/* Email change modal */}
				<Modal
					open={showModal.value}
					title="Confirm E-Mail address change"
					onSubmit$={() => {
						updateEmail(currentPassword.value, newEmail.value);
					}}
					onCancel$={() => {
						showModal.value = false;
					}}
				>
					<div q:slot="modalIcon">
						<ShieldCheckIcon forcedClass="h-10 w-10 text-primary-500" />
					</div>
					<div q:slot="modalContent" class="space-y-4">
						<p>We will send a verification E-Mail to {newEmail.value}</p>
						<div class="space-y-1">
							<label html-for="password">Confirm the change by entering your password:</label>
							<input
								type="password"
								name="password"
								onChange$={(_, el) => {
									currentPassword.value = el.value;
								}}
								class="w-full"
							/>
						</div>
						{errorMessage.value !== '' && (
							<ErrorMessage
								heading="We ran into a problem changing your E-Mail!"
								message={errorMessage.value}
							/>
						)}
					</div>
				</Modal>

				{/* Profile Edit Modal */}
				{isEditing.value && (
					<div class="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4">
						<div class="bg-white rounded-2xl shadow-xl p-6 sm:p-8 w-full max-w-lg relative max-h-[90vh] overflow-y-auto">
							<button
								class="absolute top-4 right-4 text-gray-400 hover:text-black cursor-pointer text-xl"
								onClick$={() => (isEditing.value = false)}
								title="Close"
							>
								×
							</button>
							<h2 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900 font-heading pr-8">Edit Profile</h2>
							{profileFeedback.value && (
								<div class={`mb-4 text-center text-sm font-semibold ${profileFeedback.value.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
									{profileFeedback.value}
								</div>
							)}
							<div class="space-y-4">
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
									<input
										type="text"
										value={appState.customer?.title}
										onInput$={(_, el) => (update.value.title = el.value)}
										class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-red-500 focus:border-red-500 text-sm sm:text-base p-2 sm:p-3"
									/>
								</div>
								<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div>
										<label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
										<input
											type="text"
											value={appState.customer?.firstName}
											onInput$={(_, el) => (update.value.firstName = el.value)}
											class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-red-500 focus:border-red-500 text-sm sm:text-base p-2 sm:p-3"
										/>
									</div>
									<div>
										<label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
										<input
											type="text"
											value={appState.customer?.lastName}
											onInput$={(_, el) => (update.value.lastName = el.value)}
											class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-red-500 focus:border-red-500 text-sm sm:text-base p-2 sm:p-3"
										/>
									</div>
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">E-Mail</label>
									<input
										type="email"
										value={appState.customer?.emailAddress}
										onInput$={(_, el) => (newEmail.value = el.value)}
										class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-red-500 focus:border-red-500 text-sm sm:text-base p-2 sm:p-3"
									/>
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
									<input
										type="tel"
										value={appState.customer?.phoneNumber}
										onInput$={(_, el) => (update.value.phoneNumber = el.value)}
										class="block w-full rounded-md border-gray-300 shadow-sm focus:ring-red-500 focus:border-red-500 text-sm sm:text-base p-2 sm:p-3"
									/>
								</div>
							</div>
							<div class="flex flex-col sm:flex-row gap-4 mt-6">
								<div class="flex-1">
									<HighlightedButton
										onClick$={() => {
											updateCustomer();
										}}
									>
										<CheckIcon /> &nbsp; Save
									</HighlightedButton>
								</div>
								<button
									onClick$={() => (isEditing.value = false)}
									class="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none cursor-pointer"
								>
									× &nbsp; Cancel
								</button>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
});

export const head = () => {
	return createSEOHead({
		title: 'My Account',
		description: 'Manage your account settings, view orders, update personal information and shipping addresses.',
		noindex: true,
	});
};
