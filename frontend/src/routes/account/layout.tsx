import { Slot, component$, useContext, useVisibleTask$ } from '@qwik.dev/core';
import { APP_STATE } from '~/constants';
import { getActiveCustomerQuery } from '~/providers/shop/customer/customer';

export default component$(() => {
	const appState = useContext(APP_STATE);

	useVisibleTask$(async () => {
		const activeCustomer = await getActiveCustomerQuery();
		if (activeCustomer) {
			appState.customer = {
				title: activeCustomer.title ?? '',
				firstName: activeCustomer.firstName,
				id: activeCustomer.id,
				lastName: activeCustomer.lastName,
				emailAddress: activeCustomer.emailAddress,
				phoneNumber: activeCustomer.phoneNumber ?? '',
			};
		} else {
			window.location.href = '/';
		}
	});

	return (
		<div class="min-h-screen">
			<div class="max-w-[1824px] mx-auto px-4 sm:px-6 lg:px-8 w-full">
				<Slot />
			</div>
		</div>
	);
});
