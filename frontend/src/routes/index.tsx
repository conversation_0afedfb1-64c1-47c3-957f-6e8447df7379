import { component$, useVisibleTask$, useStylesScoped$, $ } from '@qwik.dev/core';
import { Link } from '@qwik.dev/router';
import { createSEOHead } from '~/utils/seo';
import { preloadImage } from '~/utils/image-cache';

// Lazy load components for better performance - Qwik 2.0 style
import { FeaturesSection } from '~/components/home/<USER>';
import { ReviewsSection } from '~/components/home/<USER>';
import { BrandStorySection } from '~/components/home/<USER>';

// Import images for optimization - Vite will handle AVIF/WebP/JPEG conversion and optimization
// HERO SECTION
import HeroImage_480 from '~/media/hero.png?format=avif&width=480&quality=85&url';
import HeroImage_768 from '~/media/hero.png?format=avif&width=768&quality=85&url';
import HeroImage_1024 from '~/media/hero.png?format=avif&width=1024&quality=85&url';
import HeroImage_1600 from '~/media/hero.png?format=avif&width=1600&quality=85&url';
import HeroImage_2000 from '~/media/hero.png?format=avif&width=2000&quality=85&url';
import HeroImageWebP_480 from '~/media/hero.png?format=webp&width=480&quality=85&url';
import HeroImageWebP_768 from '~/media/hero.png?format=webp&width=768&quality=85&url';
import HeroImageWebP_1024 from '~/media/hero.png?format=webp&width=1024&quality=85&url';
import HeroImageWebP_1600 from '~/media/hero.png?format=webp&width=1600&quality=85&url';
import HeroImageWebP_2000 from '~/media/hero.png?format=webp&width=2000&quality=85&url';
import HeroImageJPEG_480 from '~/media/hero.png?format=jpeg&width=480&quality=95&url';
import HeroImageJPEG_768 from '~/media/hero.png?format=jpeg&width=768&quality=95&url';
import HeroImageJPEG_1024 from '~/media/hero.png?format=jpeg&width=1024&quality=95&url';
import HeroImageJPEG_1600 from '~/media/hero.png?format=jpeg&width=1600&quality=95&url';
import HeroImageJPEG_2000 from '~/media/hero.png?format=jpeg&width=2000&quality=95&url';
// SECTION 2
import Section2Image_480 from '~/media/2.png?format=avif&width=480&quality=85&url';
import Section2Image_768 from '~/media/2.png?format=avif&width=768&quality=85&url';
import Section2Image_1024 from '~/media/2.png?format=avif&width=1024&quality=85&url';
import Section2Image_1600 from '~/media/2.png?format=avif&width=1600&quality=85&url';
import Section2Image_2000 from '~/media/2.png?format=avif&width=2000&quality=85&url';
import Section2ImageWebP_480 from '~/media/2.png?format=webp&width=480&quality=85&url';
import Section2ImageWebP_768 from '~/media/2.png?format=webp&width=768&quality=85&url';
import Section2ImageWebP_1024 from '~/media/2.png?format=webp&width=1024&quality=85&url';
import Section2ImageWebP_1600 from '~/media/2.png?format=webp&width=1600&quality=85&url';
import Section2ImageWebP_2000 from '~/media/2.png?format=webp&width=2000&quality=85&url';
import Section2ImageJPEG_480 from '~/media/2.png?format=jpeg&width=480&quality=95&url';
import Section2ImageJPEG_768 from '~/media/2.png?format=jpeg&width=768&quality=95&url';
import Section2ImageJPEG_1024 from '~/media/2.png?format=jpeg&width=1024&quality=95&url';
import Section2ImageJPEG_1600 from '~/media/2.png?format=jpeg&width=1600&quality=95&url';
import Section2ImageJPEG_2000 from '~/media/2.png?format=jpeg&width=2000&quality=95&url';
// SECTION 3
import HomeLast_480 from '~/media/homelast.png?format=avif&width=480&quality=85&url';
import HomeLast_768 from '~/media/homelast.png?format=avif&width=768&quality=85&url';
import HomeLast_1024 from '~/media/homelast.png?format=avif&width=1024&quality=85&url';
import HomeLast_1600 from '~/media/homelast.png?format=avif&width=1600&quality=85&url';
import HomeLast_2000 from '~/media/homelast.png?format=avif&width=2000&quality=85&url';
import HomeLastWebP_480 from '~/media/homelast.png?format=webp&width=480&quality=85&url';
import HomeLastWebP_768 from '~/media/homelast.png?format=webp&width=768&quality=85&url';
import HomeLastWebP_1024 from '~/media/homelast.png?format=webp&width=1024&quality=85&url';
import HomeLastWebP_1600 from '~/media/homelast.png?format=webp&width=1600&quality=85&url';
import HomeLastWebP_2000 from '~/media/homelast.png?format=webp&width=2000&quality=85&url';
import HomeLastJPEG_480 from '~/media/homelast.png?format=jpeg&width=480&quality=95&url';
import HomeLastJPEG_768 from '~/media/homelast.png?format=jpeg&width=768&quality=95&url';
import HomeLastJPEG_1024 from '~/media/homelast.png?format=jpeg&width=1024&quality=95&url';
import HomeLastJPEG_1600 from '~/media/homelast.png?format=jpeg&width=1600&quality=95&url';
import HomeLastJPEG_2000 from '~/media/homelast.png?format=jpeg&width=2000&quality=95&url';

// Define styles for clean vertical scroll layout
const SLIDER_STYLES = `
 /* Prevent horizontal scroll */
 html, body {
   overflow-x: hidden;
 }
 
 .slider-image {
   image-rendering: -webkit-optimize-contrast;
   image-rendering: crisp-edges;
   backface-visibility: hidden;
   transform: translateZ(0);
   will-change: transform;
 }
 
 @supports (-webkit-touch-callout: none) {
   .slider-image {
     transform: translate3d(0,0,0);
     -webkit-transform-style: preserve-3d;
   }
 }
 
 .hero-overlay {
   background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 20%, rgba(0,0,0,0.1) 40%, rgba(0,0,0,0) 60%);
 }
 
 .feature-card {
   transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
 }
 
 .feature-card:hover {
   transform: translateY(-4px);
   box-shadow: 0 20px 40px rgba(0,0,0,0.1);
 }
 
 /* Clean vertical scroll sections - all full screen */
 .page-section {
   min-height: 100vh;
   width: 100%;
   position: relative;
   display: flex;
   flex-direction: column;
 }
 
 /* Hero section specific height and positioning */
 .hero-section {
   /* Mobile: 70vh to account for Safari URL bar */
   height: 70vh;
   min-height: 500px;
 }
 
 /* Desktop: full height */
 @media (min-width: 1024px) {
   .hero-section {
     height: 100vh;
   }
 }
 
 /* Use dynamic viewport height on supported browsers */
 @supports (height: 100dvh) {
   .hero-section {
     height: 70dvh;
   }
   @media (min-width: 1024px) {
     .hero-section {
       height: 100dvh;
     }
   }
 }
 
 /* Small screens OR touch devices: Make hero section full height and ensure proper content positioning */
 @media (max-width: 1023px) {
   .page-section.hero-section {
     height: 100vh !important;
     min-height: 100vh !important;
     display: flex;
     flex-direction: column;
   }
   
   /* Use dynamic viewport height on supported browsers for small screens */
   @supports (height: 100dvh) {
     .page-section.hero-section {
       height: 100dvh !important;
       min-height: 100dvh !important;
     }
   }
   
   /* Ensure hero content container takes full height and positions content at bottom */
   .page-section.hero-section > div:last-child {
     height: 100% !important;
     display: flex !important;
     flex-direction: column !important;
     justify-content: flex-end !important;
   }
 }
 
 /* Use dynamic viewport height on supported browsers */
 @supports (height: 100dvh) {
   .page-section {
     min-height: 100dvh;
   }
 }

 /* Vertical scroll container */
 .vertical-scroll-container {
   width: 100%;
 }

 /* Section backgrounds and layouts */
 .features-reviews-section {
   position: relative;
 }

 /* Trustpilot branding colors */
 .trustpilot-green {
   color: #00b67a;
 }
 
 .trustpilot-bg {
   background-color: #00b67a;
 }

 .trustpilot-star {
   color: #00b67a;
 }

 /* Scroll Animations */
 @media (prefers-reduced-motion: no-preference) {
   .animate-fade-up-initial {
     opacity: 0;
     transform: translateY(20px);
   }
   
   .animate-fade-up {
     opacity: 1;
     transform: translateY(0);
     transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                 transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
   }
   
   .animate-fade-left-initial {
     opacity: 0;
     transform: translateX(-30px);
   }
   
   .animate-fade-left {
     opacity: 1;
     transform: translateX(0);
     transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                 transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
   }
   
   .animate-scale-initial {
     opacity: 0;
     transform: scale(0.95);
   }
   
   .animate-scale {
     opacity: 1;
     transform: scale(1);
     transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
                 transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
   }
   
   .animate-fade-initial {
     opacity: 0;
   }
   
   .animate-fade {
     opacity: 1;
     transition: opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1);
   }
   
   /* Staggered delays */
   .animate-delay-100 { transition-delay: 0.1s; }
   .animate-delay-200 { transition-delay: 0.2s; }
   .animate-delay-300 { transition-delay: 0.3s; }
   .animate-delay-400 { transition-delay: 0.4s; }
   .animate-delay-500 { transition-delay: 0.5s; }
 }
 
 /* For users who prefer reduced motion, show elements immediately */
 @media (prefers-reduced-motion: reduce) {
   .animate-fade-up-initial,
   .animate-fade-left-initial,
   .animate-scale-initial,
   .animate-fade-initial {
     opacity: 1;
     transform: none;
   }
 }
` as const;

export default component$(() => {
 useStylesScoped$(SLIDER_STYLES);

 // Preload secondary images for better page experience
 useVisibleTask$(() => {
   // Preload common shop page assets and secondary hero images in the background
   const imagesToPreload = [
     '/asset_placeholder.webp', // Common placeholder
     // Preload next section images for smooth scrolling experience
     Section2Image_768, // Section 2 mobile
     Section2Image_1600, // Section 2 desktop
     HomeLast_768, // Section 3 mobile
     HomeLast_1600, // Section 3 desktop
   ];
   
   imagesToPreload.forEach(src => {
     preloadImage(src);
   });
 });

 // Enhanced navigation with preloading and prefetch
 const handleShopNavigation = $(() => {
   // Preload shop page images before navigation
   const shopImages = [
     '/asset_placeholder.webp',
     // Add more common shop images as needed
   ];
   
   Promise.all(shopImages.map(src => preloadImage(src))).then(() => {
     // Use programmatic navigation for the click handler
     // The Link component with prefetch will handle hover prefetching
     window.location.href = '/shop';
   });
 });

 // Scroll animation intersection observer
 useVisibleTask$(() => {
   if (typeof document === 'undefined') return;
   
   const animatedElements = document.querySelectorAll('[class*="animate-"][class*="-initial"]');
   
   const observer = new IntersectionObserver((entries) => {
     entries.forEach((entry) => {
       if (entry.isIntersecting) {
         const element = entry.target as HTMLElement;
         
         // Remove initial class and add animated class
         if (element.classList.contains('animate-fade-up-initial')) {
           element.classList.remove('animate-fade-up-initial');
           element.classList.add('animate-fade-up');
         } else if (element.classList.contains('animate-fade-left-initial')) {
           element.classList.remove('animate-fade-left-initial');
           element.classList.add('animate-fade-left');
         } else if (element.classList.contains('animate-scale-initial')) {
           element.classList.remove('animate-scale-initial');
           element.classList.add('animate-scale');
         } else if (element.classList.contains('animate-fade-initial')) {
           element.classList.remove('animate-fade-initial');
           element.classList.add('animate-fade');
         }
         
         // Stop observing this element
         observer.unobserve(element);
       }
     });
   }, {
     threshold: 0.2, // Trigger when 20% of element is visible
     rootMargin: '0px 0px -50px 0px' // Offset to trigger slightly later
   });
   
   animatedElements.forEach((element) => {
     observer.observe(element);
   });
   
   return () => {
     observer.disconnect();
   };
 });

	return (
		<div class="vertical-scroll-container">
			{/* Section 1: Hero Section */}
			<section class="page-section hero-section relative overflow-hidden">
				{/* Hero Background Image */}
				<div class="absolute inset-0">
					<picture>
						<source
							type="image/avif"
							srcset={`${HeroImage_480} 480w,${HeroImage_768} 768w,${HeroImage_1024} 1024w,${HeroImage_1600} 1600w,${HeroImage_2000} 2000w`}
							sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
						/>
						<source
							type="image/webp"
							srcset={`${HeroImageWebP_480} 480w,${HeroImageWebP_768} 768w,${HeroImageWebP_1024} 1024w,${HeroImageWebP_1600} 1600w,${HeroImageWebP_2000} 2000w`}
							sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
						/>
						<source
							type="image/jpeg"
							srcset={`${HeroImageJPEG_480} 480w,${HeroImageJPEG_768} 768w,${HeroImageJPEG_1024} 1024w,${HeroImageJPEG_1600} 1600w,${HeroImageJPEG_2000} 2000w`}
							sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
						/>
						<img
							src={HeroImageJPEG_1024}
							alt="Premium Knife from Damned Designs"
							loading="eager"
							fetchPriority="high"
							width={1024}
							height={683}
							style={{ width: '100%', height: '100%', objectFit: 'cover' }}
						/>
					</picture>
					{/* Dark overlay for text readability */}
					<div class="absolute inset-0 hero-overlay"></div>
				</div>

				{/* Hero Content - Mobile: bottom with extra padding for URL bar, Desktop: bottom-left text, bottom-right button */}
				<div class="relative z-10 h-full flex flex-col items-center justify-end lg:flex-row lg:items-end lg:justify-between px-6 sm:px-8 lg:px-16 xl:px-20 pb-28 sm:pb-32 lg:pb-16">
						{/* Text Content - Mobile: centered, Desktop: bottom left */}
						<div class="text-center lg:text-left max-w-2xl mb-8 lg:mb-0">
							{/* Main Hero Title - Large, Bold, White */}
							<h1 class="font-heading tracking-wider text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-none mb-4 lg:mb-6">
								PRECISION CRAFTED TOOLS,
							</h1>

							{/* Hero Subtitle */}
							<p class="font-body text-sm sm:text-base lg:text-lg xl:text-xl text-white lg:text-[#e34545] font-bold tracking-wider">
								FOR THE DISCERNING COLLECTOR.
							</p>
						</div>

						{/* CTA Button - Mobile: centered below text, Desktop: bottom right */}
						<Link 
							href="/shop" 
							prefetch
							class="bg-[#e34545] hover:bg-[#d32f2f] text-white px-6 py-3 sm:px-8 sm:py-4 text-sm sm:text-base lg:text-lg font-bold tracking-wide transition-all duration-300 transform hover:scale-105 hover:shadow-xl uppercase whitespace-nowrap rounded-full cursor-pointer inline-block text-center"
							onClick$={handleShopNavigation}
						>
							SHOP NOW →
						</Link>
					</div>
				</section>

				{/* Section 2: Features & Reviews */}
				<section class="page-section features-reviews-section justify-center">
					{/* Background Image */}
					<div class="absolute inset-0">
						<picture>
							<source
								type="image/avif"
								srcset={`${Section2Image_480} 480w,${Section2Image_768} 768w,${Section2Image_1024} 1024w,${Section2Image_1600} 1600w,${Section2Image_2000} 2000w`}
								sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
							/>
							<source
								type="image/webp"
								srcset={`${Section2ImageWebP_480} 480w,${Section2ImageWebP_768} 768w,${Section2ImageWebP_1024} 1024w,${Section2ImageWebP_1600} 1600w,${Section2ImageWebP_2000} 2000w`}
								sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
							/>
							<source
								type="image/jpeg"
								srcset={`${Section2ImageJPEG_480} 480w,${Section2ImageJPEG_768} 768w,${Section2ImageJPEG_1024} 1024w,${Section2ImageJPEG_1600} 1600w,${Section2ImageJPEG_2000} 2000w`}
								sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
							/>
							<img
								src={Section2ImageJPEG_1024}
								alt="Damned Designs Background"
								loading="lazy"
								width={1024}
								height={683}
								style={{ width: '100%', height: '100%', objectFit: 'cover' }}
							/>
						</picture>
						{/* Header gradient overlay only */}
						<div class="absolute inset-0 hero-overlay"></div>
					</div>

					<div class="relative z-10 w-full flex items-center">
						<div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-16 w-full">
							{/* Mobile: Stack vertically with better spacing, Desktop: Keep 2-column grid */}
							<div class="flex flex-col space-y-4 lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center lg:space-y-0 h-full py-4 lg:py-8"> 
								
								{/* Mobile: Features first for better mobile UX, Desktop: Trustpilot Reviews (left side) */}
								<div class="order-2 lg:order-1 animate-fade-left-initial">
									<ReviewsSection />
								</div>

								{/* Mobile: Features first for mobile UX, Desktop: Features (right side) */}
								<div class="order-1 lg:order-2 animate-fade-up-initial">
									<FeaturesSection />
								</div>

							</div>
						</div>
					</div>
				</section>

				{/* Section 3: Brand Story with Integrated Footer */}
				<section class="page-section relative overflow-hidden">
					{/* Background Image */}
					<div class="absolute inset-0">
						<picture>
							<source
								type="image/avif"
								srcset={`${HomeLast_480} 480w,${HomeLast_768} 768w,${HomeLast_1024} 1024w,${HomeLast_1600} 1600w,${HomeLast_2000} 2000w`}
								sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
							/>
							<source
								type="image/webp"
								srcset={`${HomeLastWebP_480} 480w,${HomeLastWebP_768} 768w,${HomeLastWebP_1024} 1024w,${HomeLastWebP_1600} 1600w,${HomeLastWebP_2000} 2000w`}
								sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
							/>
							<source
								type="image/jpeg"
								srcset={`${HomeLastJPEG_480} 480w,${HomeLastJPEG_768} 768w,${HomeLastJPEG_1024} 1024w,${HomeLastJPEG_1600} 1600w,${HomeLastJPEG_2000} 2000w`}
								sizes="(max-width: 600px) 100vw, (max-width: 1200px) 80vw, 60vw"
							/>
							<img
								src={HomeLastJPEG_1024}
								alt="Damned Designs Background"
								loading="lazy"
								width={1024}
								height={683}
								style={{ width: '100%', height: '100%', objectFit: 'cover' }}
							/>
						</picture>
						{/* Light overlay to darken image slightly for text readability */}
						<div class="absolute inset-0 bg-black/20"></div>
					</div>

					{/* Content - Simplified structure with consistent spacing */}
					<div class="relative z-10 h-full flex flex-col justify-center px-6 sm:px-8 lg:px-16 xl:px-20 py-16">
						<div class="mx-auto w-full h-full flex flex-col justify-center max-w-7xl gap-8 animate-fade-up-initial">
							<BrandStorySection />
						</div>
					</div>
				</section>
		</div>
	);
});

export const head = () => {
	return createSEOHead({
		title: 'Damned Designs - Precision Crafted Knives',
		description: 'Premium handcrafted knives and tools. Shop our unique collection of custom blades, EDC gear, and more.',
		noindex: false,
		links: [
			// Preload critical hero images for optimal LCP
			// Mobile-first: 768w for most mobile devices
			{
				rel: 'preload',
				as: 'image',
				href: HeroImage_768,
				type: 'image/avif',
				media: '(max-width: 1023px)',
			},
			{
				rel: 'preload',
				as: 'image',
				href: HeroImageWebP_768,
				type: 'image/webp',
				media: '(max-width: 1023px)',
			},
			// Desktop: 1600w for larger screens
			{
				rel: 'preload',
				as: 'image',
				href: HeroImage_1600,
				type: 'image/avif',
				media: '(min-width: 1024px)',
			},
			{
				rel: 'preload',
				as: 'image',
				href: HeroImageWebP_1600,
				type: 'image/webp',
				media: '(min-width: 1024px)',
			},
		],
	});
};