import { LoadingSpinner } from '~/components/loading-spinner/loading-spinner';
import {
	$,
	Slot,
	component$,
	useContextProvider,
	useOn,
	useStore,
	useVisibleTask$,
} from '@qwik.dev/core';
import { RequestHandler, routeLoader$, useLocation } from '@qwik.dev/router';
import { ImageTransformerProps, useImageProvider } from 'qwik-image';
import Menu from '~/components/menu/Menu';
import { APP_STATE, CUSTOMER_NOT_DEFINED_ID, IMAGE_RESOLUTIONS } from '~/constants';
import { Order } from '~/generated/graphql';
import { getAvailableCountriesQuery } from '~/providers/shop/checkout/checkout';
import { getCollections } from '~/providers/shop/collections/collections';
import { getActiveOrderQuery } from '~/providers/shop/orders/order';
import { ActiveCustomer, AppState } from '~/types';
import Cart from '../components/cart/Cart';
import Footer from '../components/footer/footer';
import Header from '../components/header/header';
import { CartProvider } from '~/contexts/CartContext';

export const onGet: RequestHandler = async ({ cacheControl }) => {
	// For pages: short cache with stale-while-revalidate for fresh content
	cacheControl({ staleWhileRevalidate: 60 * 60 * 24 * 7, maxAge: 60 * 5 }); // 5 minutes fresh, 7 days stale
};

export const useCollectionsLoader = routeLoader$(async () => {
	return await getCollections();
});

export const useAvailableCountriesLoader = routeLoader$(async () => {
	return await getAvailableCountriesQuery();
});

export const onRequest: RequestHandler = () => {
	// Handler for request processing
};

export default component$(() => {
	const location = useLocation();
	const isHomePage = location.url.pathname === '/';
	
	const imageTransformer$ = $(({ src, width, height }: ImageTransformerProps): string => {
		return `${src}?w=${width}&h=${height}&format=webp`;
	});

	// Provide your default options
	useImageProvider({
		imageTransformer$,
		resolutions: IMAGE_RESOLUTIONS,
	});

	const collectionsSignal = useCollectionsLoader();
	const availableCountriesSignal = useAvailableCountriesLoader();

	const state = useStore<AppState>({
		showCart: false,
		showMenu: false,
		showUserMenu: false,
		showMobileUserMenu: false,
		isLoading: false,
		customer: { id: CUSTOMER_NOT_DEFINED_ID, firstName: '', lastName: '', emailAddress: '' } as ActiveCustomer,
		activeOrder: {} as Order,
		collections: collectionsSignal.value || [],
		availableCountries: availableCountriesSignal.value || [],
		shippingAddress: {
			id: '',
			city: '',
			company: '',
			countryCode: '', // Start with empty, let Shipping component set the default
			fullName: '',
			phoneNumber: '',
			postalCode: '',
			province: '',
			streetLine1: '',
			streetLine2: '',
		},
		billingAddress: {
			firstName: '',
			lastName: '',
			streetLine1: '',
			streetLine2: '',
			city: '',
			province: '',
			postalCode: '',
			countryCode: ''
		},
		addressBook: [],
	});

	useContextProvider(APP_STATE, state);

	useVisibleTask$(({ track }) => {
		track(() => location.isNavigating);
		state.isLoading = location.isNavigating;
	});

	useVisibleTask$(async () => {
		const activeOrder = await getActiveOrderQuery();

		// Only set activeOrder if it's a valid order with items
		// This helps clear the cart when order is completed or invalid
		if (activeOrder && activeOrder.id && activeOrder.lines && activeOrder.lines.length > 0) {
			state.activeOrder = activeOrder;
		} else {
			// Clear the cart if no valid active order
			state.activeOrder = {} as Order;
		}
	});
	useVisibleTask$(({ track }) => {
		track(() => state.showCart);
		track(() => state.showMenu);

		if (state.showCart || state.showMenu) {
			document.body.classList.add('overflow-hidden');
		} else {
			document.body.classList.remove('overflow-hidden');
		}
	});

	useOn(
		'keydown',
		$((event: unknown) => {
			if ((event as KeyboardEvent).key === 'Escape') {
				state.showCart = false;
				state.showMenu = false;
			}
		})
	);
	return (
		<CartProvider>
			<div>
				{state.isLoading && <LoadingSpinner />}
				<Header />
				{/* Conditional Cart Loading: Only load full cart component when needed */}
				{!isHomePage ? (
					// Non-homepage: Load cart immediately for better UX
					<Cart />
				) : (
					// Homepage: Lazy load cart only when showCart is true (user clicks cart icon)
					state.showCart && <Cart />
				)}
				<Menu />
				<main class={`min-h-screen ${isHomePage ? '' : 'pt-16'}`}>
					<Slot />
				</main>
				{/* Only show footer on non-homepage pages since homepage has integrated footer */}
				{!isHomePage && <Footer />}
			</div>
		</CartProvider>
	);
});
