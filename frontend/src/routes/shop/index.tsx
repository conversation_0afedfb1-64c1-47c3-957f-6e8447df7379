import { $, component$, useStore, useTask$, useVisibleTask$ } from '@builder.io/qwik';
import { routeLoader$ } from '@qwik.dev/router';
import ProductCard from '~/components/products/ProductCard';
import ViewportLazyProductCard from '~/components/products/ViewportLazyProductCard';
import ProductSkeleton from '~/components/ui/ProductSkeleton';
import { generateImagePreloadLinks } from '~/components/ui';
import { Collection, SearchResponse } from '~/generated/graphql';
import { getCollections } from '~/providers/shop/collections/collections';
import { searchQueryWithTerm } from '~/providers/shop/products/products';
import { FacetWithValues } from '~/types';
import { createSEOHead } from '~/utils/seo';
import Filters from '~/components/Filters';
import { preloadImage } from '~/utils/image-cache';

// Define hardcoded filters based on user-provided facet values and desired order
const HARDCODED_SHOP_FILTERS: FacetWithValues[] = [
 {
 id: 'category',
 name: 'Category',
 open: true,
 values: [
  { id: '1', name: 'folding knives', selected: false },
  { id: '3', name: 'fixed blades', selected: false },
  { id: '5', name: 'edc', selected: false },
  { id: '2', name: 'osiris chef knives', selected: false },
  { id: '6', name: 'fidget', selected: false },
  { id: '4', name: 'apparel', selected: false },
 ],
 },
];

export const useSearchLoader = routeLoader$(async () => {
 // Get all products initially - no pagination limit
 const initialSearch = await searchQueryWithTerm('', '', [], 0, 1000); // Load up to 1000 products
 const collections = await getCollections();

 return {
 currentSearch: initialSearch,
 collections
 };
});

export default component$(() => {
 const searchSignal = useSearchLoader();

 const state = useStore<{
 showMenu: boolean;
 search: SearchResponse;
 collections: Collection[];
 allPossibleFacetValues: FacetWithValues[];
 facetValues: FacetWithValues[];
 facetValueIds: string[];
 searchTerm: string;
 isLoading: boolean;
 inStockOnly: boolean;
 }>({
 showMenu: false,
 search: searchSignal.value.currentSearch as SearchResponse,
 collections: searchSignal.value.collections as Collection[],
 allPossibleFacetValues: JSON.parse(JSON.stringify(HARDCODED_SHOP_FILTERS)),
 facetValueIds: [],
 facetValues: HARDCODED_SHOP_FILTERS.map(facet => ({
  ...facet,
  values: facet.values.map(value => ({ ...value, selected: false })),
 })),
 searchTerm: '',
 isLoading: false,
 inStockOnly: true, // Default to showing only in-stock products
 });

 // Search function without pagination
 const searchProducts = $((facetValueIds: string[], searchTerm: string) => {
 return new Promise<void>((resolve) => {
  setTimeout(async () => {
   state.isLoading = true;
   
   try {
    const result = await searchQueryWithTerm('', searchTerm, facetValueIds, 0, 1000);
    state.search = result;
   } catch (error) {
    console.error('Search error:', error);
   } finally {
    state.isLoading = false;
    resolve();
   }
  }, 300); // 300ms debounce
 });
 });

 useTask$(async ({ track }) => {
 track(() => state.facetValueIds);
 track(() => state.searchTerm);

 // Search when filters change
 await searchProducts(state.facetValueIds, state.searchTerm);
 
 // Update facet values display
 state.facetValues = state.allPossibleFacetValues.map(facet => ({
  ...facet,
  values: facet.values.map(value => ({
  ...value,
  selected: state.facetValueIds.includes(value.id),
  })),
 }));
 });

 // Optimized preloading - only preload visible images
 useVisibleTask$(() => {
   if (state.search?.items) {
     // Only preload first 6 items (2 rows on mobile, 1 row on desktop)
     state.search.items.slice(0, 6).forEach((item: any) => {
       if (item.productAsset?.preview) {
         preloadImage(item.productAsset.preview + '?preset=medium');
       }
     });
   }
 });

 const onFilterChange = $((id: string) => {
 const currentActiveIds = state.facetValueIds;
 let newActiveIds: string[] = currentActiveIds;
 let performUpdate = false;

 if (id === 'CLEAR_ALL') {
  if (currentActiveIds.length !== 0) {
  newActiveIds = [];
  performUpdate = true;
  }
 } else {
  if (currentActiveIds.length !== 1 || currentActiveIds[0] !== id) {
  newActiveIds = [id];
  performUpdate = true;
  }
 }

 if (performUpdate) {
  state.facetValueIds = newActiveIds;
  state.facetValues = state.allPossibleFacetValues.map(facet => ({
  ...facet,
  values: facet.values.map(value => ({
   ...value,
   selected: newActiveIds.includes(value.id),
  })),
  }));
 }
 });

 const onSearchChange = $((newTerm: string) => {
 state.searchTerm = newTerm;
 });

 return (
 <div class="bg-linear-to-br from-gray-50 via-white to-gray-50 min-h-screen relative overflow-hidden">
  <div class="absolute inset-0 opacity-[0.02] pointer-events-none">
   <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(0,0,0,0.15) 1px, transparent 0); background-size: 40px 40px;"></div>
  </div>
  
  <div
  class="relative max-w-content-wide mx-auto px-4 sm:px-6 lg:px-8 py-6"
  onKeyDown$={(event: KeyboardEvent) => {
   if (event.key === 'Escape') {
   state.showMenu = false;
   }
  }}
  >
  <div class="mb-3">
   <Filters
    facetsWithValues={state.facetValues}
    facetValueIds={state.facetValueIds}
    onFilterChange$={onFilterChange}
    searchTerm={state.searchTerm}
    onSearchChange$={onSearchChange}
   />
  </div>

  {/* Combined In-Stock Toggle and Product Count */}
  <div class="mb-4 flex items-center justify-between">
   <div class="flex items-center gap-4">
    <label class="inline-flex items-center cursor-pointer">
     <input
      type="checkbox"
      class="sr-only"
      checked={state.inStockOnly}
      onChange$={(e) => {
        state.inStockOnly = (e.target as HTMLInputElement).checked;
      }}
     />
     <div class={`relative w-11 h-6 rounded-full transition-colors duration-200 ${
       state.inStockOnly ? 'bg-[#e34545]' : 'bg-gray-200'
     }`}>
      <div class={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200 ${
        state.inStockOnly ? 'translate-x-5' : 'translate-x-0'
      }`}></div>
     </div>
    </label>
    
    <p class="text-sm text-gray-600 font-medium">
     {state.isLoading ? (
      <span class="inline-flex items-center">
       <div class="w-4 h-4 border-2 border-gray-300 border-t-[#e34545] rounded-full animate-spin mr-2"></div>
       Loading premium collection...
      </span>
     ) : (
      <span>
       {(() => {
         // Calculate the actual displayed count
         let displayProducts = state.search.items;
         if (state.inStockOnly) {
           displayProducts = displayProducts.filter(product => product.inStock);
         }
         const count = displayProducts.length;
         
         return (
           <>
             Showing <span class="font-bold text-gray-900">{count}</span>{' '}
             {state.inStockOnly ? 'in-stock' : 'all'} {count === 1 ? 'product' : 'products'}
             {state.searchTerm && <span class="text-[#e34545] font-medium"> for "{state.searchTerm}"</span>}
           </>
         );
       })()}
      </span>
     )}
    </p>
   </div>
   
   {!state.isLoading && state.search.totalItems > 0 && (
    <div class="flex items-center text-sm text-gray-500 min-w-0">
     <span class="hidden sm:inline truncate">
      {state.facetValueIds.length > 0 ? (
       <>Premium {state.facetValues.flatMap(facet => 
        facet.values.filter(value => state.facetValueIds.includes(value.id))
       ).map(value => value.name).join(', ')}</>
      ) : (
       'Premium collection'
      )}
     </span>
     <div class="flex items-center ml-2 shrink-0">
      <div class="w-1.5 h-1.5 bg-[#e34545] rounded-full"></div>
     </div>
    </div>
   )}
  </div>
  
  <div class="grid grid-cols-2 gap-3 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4">
   {state.isLoading && state.search.items.length === 0 ? (
    Array.from({ length: 8 }, (_, i) => (
     <ProductSkeleton key={`skeleton-${i}`} />
    ))
   ) : state.search.items.length === 0 ? (
    <div class="col-span-full flex flex-col items-center justify-center py-16 text-center">
     <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
      <svg class="w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.172 16.172a4 4 0 015.656 0M9 12h6M12 8v.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
     </div>
     <h3 class="text-xl font-bold text-gray-900 mb-2">No products found</h3>
     <p class="text-gray-600 mb-6 max-w-sm">
      {state.searchTerm ? `We couldn't find any products matching "${state.searchTerm}".` : 'No products match your current filters.'}
     </p>
     <button
      class="px-6 py-3 bg-[#e34545] text-white rounded-full font-bold hover:bg-[#c73333] transition-colors duration-300 shadow-lg hover:shadow-xl"
      onClick$={() => {
       state.searchTerm = '';
       state.facetValueIds = [];
       state.inStockOnly = false;
       state.facetValues = state.allPossibleFacetValues.map(facet => ({
        ...facet,
        values: facet.values.map(value => ({ ...value, selected: false })),
       }));
      }}
     >
      Clear all filters
     </button>
    </div>
   ) : (
    <>
    {(() => {
     // Filter and sort products for display
     let displayProducts = state.search.items;
     
     // Apply in-stock filter if enabled
     if (state.inStockOnly) {
       displayProducts = displayProducts.filter(product => product.inStock);
     }
     
     // Sort: in-stock first, then alphabetically
     displayProducts = displayProducts.sort((a, b) => {
       // First priority: in-stock products
       if (a.inStock && !b.inStock) return -1;
       if (!a.inStock && b.inStock) return 1;
       
       // Second priority: alphabetical by product name
       return a.productName.localeCompare(b.productName);
     });
     
     return displayProducts.map((item, index) => {
       const isAboveFold = index < 4;
       
       return isAboveFold ? (
        <ProductCard
         key={item.productId}
         productAsset={item.productAsset}
         productName={item.productName}
         slug={item.slug}
         priceWithTax={item.priceWithTax}
         currencyCode={item.currencyCode}
         inStock={item.inStock}
         priority={true}
        />
       ) : (
        <ViewportLazyProductCard
         key={item.productId}
         productAsset={item.productAsset}
         productName={item.productName}
         slug={item.slug}
         priceWithTax={item.priceWithTax}
         currencyCode={item.currencyCode}
         inStock={item.inStock}
        />
       );
     });
    })()}
    </>
   )}
  </div>

  </div>
 </div>
 );
});

export const head = ({ resolveValue, url }: { resolveValue: any, url: URL }) => {
	const { currentSearch } = resolveValue(useSearchLoader);
	
	let imagePreloadLinks: any[] = [];
	
	if (currentSearch?.items && currentSearch.items.length > 0) {
		const firstRowProducts = currentSearch.items.slice(0, 4);
		
		firstRowProducts.forEach((item: any) => {
			if (item.productAsset?.preview) {
				imagePreloadLinks.push(
					...generateImagePreloadLinks(item.productAsset.preview, 'productCard', ['avif', 'webp'])
				);
			}
		});
	}
	
	return createSEOHead({
		title: 'Shop All Premium Knives & Tools',
		description: 'Browse our complete collection of premium handcrafted knives and everyday carry tools. Find the perfect blade for collectors and professionals.',
		canonical: url.href,
		links: imagePreloadLinks,
	});
};
