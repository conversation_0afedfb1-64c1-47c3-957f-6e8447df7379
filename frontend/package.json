{"name": "damned-designs-storefront", "description": "A headless commerce storefront for Damned Designs built with Vendure & Qwik", "homepage": "https://damneddesigns.com/", "license": "UNLICENSED", "private": true, "type": "module", "engines": {"node": ">=16"}, "scripts": {"build": "QWIK_LOG_LEVEL=silent qwik build", "build.verbose": "qwik build", "build:progress": "./build-with-progress.sh", "build.client": "vite build", "build.preview": "vite build --ssr src/entry.preview.tsx", "build.server": "vite build -c adapters/express/vite.config.mts", "build.types": "tsc --incremental --noEmit", "develop": "concurrently pnpm:dev pnpm:dev.server", "dev": "vite --mode ssr --port 8080", "dev.server": "cd one-click-deploy && yarn dev", "dev.debug": "node --inspect-brk ./node_modules/vite/bin/vite.js --mode ssr --force", "fmt": "prettier --write .", "fmt.check": "prettier --check .", "lint": "eslint \"src/**/*.ts*\"", "prepare": "husky install", "preview": "qwik build preview && vite preview --open", "preview:server": "vite preview", "serve": "node server/entry.express", "start": "pnpm generate && vite --open --mode ssr", "qwik": "qwik", "generate-shop": "DOTENV_CONFIG_PATH=.env graphql-codegen -r dotenv/config --config codegen-shop.ts", "generate-dev": "export IS_DEV=TRUE && pnpm generate-shop", "generate-local": "export IS_LOCAL=TRUE && pnpm generate-shop", "generate": "pnpm generate-shop"}, "devDependencies": {"@eslint/js": "^9.30.1", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/introspection": "^4.0.3", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@qwik.dev/core": "2.0.0-beta.2", "@qwik.dev/router": "2.0.0-beta.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@types/braintree-web-drop-in": "^1.39.4", "@types/compression": "^1.8.1", "@types/eslint": "^9.6.1", "@types/express": "^5.0.3", "@types/node": "^24.0.10", "@types/validator": "^13.15.2", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "compression": "^1.8.0", "concurrently": "^9.2.0", "dotenv": "^17.0.1", "eslint": "^9.30.1", "eslint-plugin-qwik": "2.0.0-beta.1", "express": "^5.1.0", "globals": "^16.3.0", "husky": "^9.1.7", "node-fetch": "3.3.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "pretty-quick": "^4.2.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "undici": "^7.11.0", "vite": "^7.0.2", "vite-imagetools": "^7.1.0", "vite-plugin-compression": "^0.5.1", "vite-tsconfig-paths": "^5.1.4"}, "dependencies": {"@angular/localize": "^20.0.6", "@graphql-codegen/typescript-generic-sdk": "^4.0.2", "@types/grecaptcha": "^3.0.9", "card-validator": "^10.0.3", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "qwik-image": "0.0.16", "us-state-converter": "^1.0.8", "validator": "^13.15.15", "zod": "^3.25.73"}, "overrides": {"@angular/compiler-cli": {"typescript": "5.3.3"}}}