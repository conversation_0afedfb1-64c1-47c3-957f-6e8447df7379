/**
 * Bulk Tracking Number Update Script
 * 
 * This script updates tracking numbers for multiple orders and triggers
 * the same email notifications as the Vendure Admin UI.
 * 
 * Usage:
 * 1. Create a CSV file named tracking-data.csv with columns: order_code,tracking_code
 * 2. Run: node bulk-tracking-update.js [--dry-run]
 *
 * Options:
 * --dry-run: Test the script without making any changes or sending emails
 */

import fs from 'fs';
import { createReadStream } from 'fs';
import csv from 'csv-parser';
import fetch from 'node-fetch';

// Argument parsing
const args = process.argv.slice(2);
let csvFilePath = './tracking-data.csv';

const fileIndex = args.indexOf('--file');
if (fileIndex > -1 && args[fileIndex + 1]) {
    csvFilePath = args[fileIndex + 1];
} else if (fileIndex > -1) {
    console.error('Error: --file option requires a path.');
    process.exit(1);
}

const isDryRun = args.includes('--dry-run');

// Configuration
const ADMIN_API_URL = 'http://localhost:3000/admin-api';

// Admin credentials
const USERNAME = 'damned'; // Replace with your admin username
const PASSWORD = 'Superadmin@'; // Replace with your admin password

// GraphQL queries
const LOGIN_MUTATION = `
  mutation Login($username: String!, $password: String!) {
    authenticate(input: { native: { username: $username, password: $password } }) {
      ... on CurrentUser {
        id
        identifier
        channels {
          token
          code
          permissions
        }
      }
      ... on ErrorResult {
        errorCode
        message
      }
    }
  }
`;

const GET_ORDERS_QUERY = `
  query GetOrders($options: OrderListOptions) {
    orders(options: $options) {
      items {
        id
        code
        state
        fulfillments {
          id
          trackingCode
        }
      }
      totalItems
    }
  }
`;

const UPDATE_FULFILLMENT_MUTATION = `
  mutation UpdateFulfillment($id: ID!, $trackingCode: String!) {
    modifyFulfillment(input: {
      fulfillmentId: $id,
      trackingCode: $trackingCode
    }) {
      id
      trackingCode
      ... on Fulfillment {
        trackingCode
      }
    }
  }
`;

// Helper function for GraphQL requests
async function graphqlRequest(query, variables, token) {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  // Add Authorization header if token is provided
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  const response = await fetch(ADMIN_API_URL, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      query,
      variables,
    }),
  });
  
  const result = await response.json();
  if (result.errors) {
    throw new Error(result.errors[0].message);
  }
  
  return result.data;
}

// Main function
async function updateTrackingNumbers() {
  try {
    // Print mode
    if (isDryRun) {
      console.log('🔍 DRY RUN MODE: No changes will be made');
    } else {
      console.log('💡 LIVE MODE: Tracking numbers will be updated and emails sent');
    }
    
    // Step 1: Login to establish authenticated session
    console.log('Logging in to Vendure Admin API...');
    const loginResult = await graphqlRequest(LOGIN_MUTATION, {
      username: USERNAME,
      password: PASSWORD,
    });
    
    if (!loginResult.authenticate || loginResult.authenticate.errorCode) {
      const errorMessage = loginResult.authenticate?.message || 'Unknown authentication error';
      console.error(`❌ Login failed: ${errorMessage}`);
      process.exit(1);
    }
    
    console.log('✅ Login successful as:', loginResult.authenticate.identifier);
    
    // Extract the token from the first channel
    if (!loginResult.authenticate.channels || loginResult.authenticate.channels.length === 0) {
      console.error('❌ No channels found in authentication response');
      process.exit(1);
    }
    
    const channel = loginResult.authenticate.channels[0];
    const token = channel.token;
    console.log(`🔑 Got API token for channel: ${channel.code}`)
    console.log(`🔑 Channel permissions:`, channel.permissions);
    console.log(`🔑 Connected to Vendure Admin API`);
    console.log('-------------------------------------------');
    
    // Step 2: Read tracking data from CSV
    const trackingData = [];
    
    await new Promise((resolve, reject) => {
      createReadStream(csvFilePath)
        .pipe(csv())
        .on('data', (row) => {
          trackingData.push({
            orderCode: row.order_code,
            trackingCode: row.tracking_code,
          });
        })
        .on('end', resolve)
        .on('error', reject);
    });
    
    console.log(`📋 Read ${trackingData.length} tracking entries from CSV`);
    
    // Step 3: Process each order
    let successCount = 0;
    let errorCount = 0;
    
    for (const item of trackingData) {
      try {
        // Get order details including fulfillment IDs
        const orderData = await graphqlRequest(GET_ORDERS_QUERY, {
          options: {
            filter: {
              code: { eq: item.orderCode }
            }
          }
        }, token);
        
        if (!orderData.orders || !orderData.orders.items || orderData.orders.items.length === 0) {
          console.error(`Order not found: ${item.orderCode}`);
          errorCount++;
          continue;
        }
        
        const order = orderData.orders.items[0];
        
        if (!order.fulfillments || order.fulfillments.length === 0) {
          console.error(`No fulfillments found for order: ${item.orderCode}`);
          errorCount++;
          continue;
        }
        
        // In dry-run mode, just report what would be updated
        if (isDryRun) {
          console.log(`🔍 Would update order ${item.orderCode} with tracking: ${item.trackingCode}`);
          console.log(`   Order state: ${order.state}`);
          console.log(`   Current tracking: ${order.fulfillments.map(f => f.trackingCode || 'none').join(', ')}`);
          console.log(`   Fulfillment IDs: ${order.fulfillments.map(f => f.id).join(', ')}`);
          successCount++;
        } else {
          // Update each fulfillment with the tracking code
          // This uses the same API as the Admin UI, so it will trigger emails
          for (const fulfillment of order.fulfillments) {
            await graphqlRequest(UPDATE_FULFILLMENT_MUTATION, {
              id: fulfillment.id,
              trackingCode: item.trackingCode,
            }, token);
          }
          
          console.log(`✅ Updated tracking for order ${item.orderCode}: ${item.trackingCode}`);
          successCount++;
        }
      } catch (err) {
        console.error(`Error updating order ${item.orderCode}: ${err.message}`);
        errorCount++;
      }
    }
    
    console.log('\n-------------------------------------------');
    if (isDryRun) {
      console.log('🔍 DRY RUN COMPLETED - No changes were made');
      console.log(`✅ Orders verified and ready for update: ${successCount}`);
      console.log(`❌ Orders with issues: ${errorCount}`);
      console.log('\nTo perform the actual update, run without the --dry-run flag');
    } else {
      console.log('✅ BULK UPDATE COMPLETED');
      console.log(`✅ Successfully updated: ${successCount}`);
      console.log(`❌ Failed updates: ${errorCount}`);
      console.log('\nEmails have been sent to customers with tracking information');
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

// Run the script
updateTrackingNumbers();
