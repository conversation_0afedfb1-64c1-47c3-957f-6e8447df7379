#!/bin/bash
# Bulk Tracking Number Update Script with Email Notifications
# 
# This script updates tracking numbers for multiple orders and triggers
# the same email notifications as the Vendure Admin UI.
#
# Usage: 
#   ./bulk-tracking-update.sh [--dry-run] tracking-data.csv

# Function to display help
show_help() {
  echo "Bulk Tracking Number Update Tool"
  echo "--------------------------------"
  echo "This tool updates tracking numbers for multiple orders and sends email notifications."
  echo ""
  echo "Usage:"
  echo "  ./bulk-tracking-update.sh [--dry-run] tracking-data.csv"
  echo ""
  echo "Options:"
  echo "  --dry-run    Test the script without making changes or sending emails"
  echo "  --help       Show this help message"
  echo ""
  echo "CSV format should be:"
  echo "order_code,tracking_code"
  echo "DD12345,USPS12345678"
  echo "DD67890,USPS87654321"
  echo ""
  echo "Example:"
  echo "  ./bulk-tracking-update.sh --dry-run tracking-data.csv    # Test without making changes"
  echo "  ./bulk-tracking-update.sh tracking-data.csv             # Update tracking and send emails"
}

# Check for help flag
if [[ "$1" == "--help" || "$#" -eq 0 ]]; then
  show_help
  exit 0
fi

# Check if pnpm packages are installed
if [ ! -d "node_modules" ]; then
  echo "Installing required packages with pnpm..."
  pnpm install
fi

# Parse arguments
DRY_RUN=""
CSV_FILE=""

if [[ "$1" == "--dry-run" ]]; then
  DRY_RUN="--dry-run"
  CSV_FILE="$2"
else
  CSV_FILE="$1"
fi

# Check for input file
if [ -z "$CSV_FILE" ]; then
  echo "Error: No CSV file specified"
  show_help
  exit 1
fi

if [ ! -f "$CSV_FILE" ]; then
  echo "Error: File $CSV_FILE not found"
  exit 1
fi

# Run the Node.js script
if [ -n "$DRY_RUN" ]; then
  echo "🔍 TESTING MODE: Checking orders without making changes..."
else
  echo "💡 LIVE MODE: Updating tracking numbers and sending email notifications..."
fi

node /home/<USER>/damneddesigns/database/bulk-tracking-update.js $DRY_RUN "$CSV_FILE"
