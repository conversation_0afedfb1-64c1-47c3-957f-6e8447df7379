import fetch from 'node-fetch';

// --- Configuration ---
const ADMIN_API_URL = 'http://localhost:3000/admin-api';
const USERNAME = 'damned'; // Use the same admin username
const PASSWORD = 'Superadmin@'; // IMPORTANT: Replace with the real password before running

// --- GraphQL Definitions ---
const LOGIN_MUTATION = `
  mutation Login($username: String!, $password: String!) {
    authenticate(input: { native: { username: $username, password: $password } }) {
      ... on CurrentUser {
        id
        identifier
        channels { token permissions }
      }
      ... on ErrorResult {
        errorCode
        message
      }
    }
  }
`;

const GET_CURRENT_USER_QUERY = `
  query {
    me {
      id
      identifier
      roles {
        code
      }
    }
  }
`;

// --- Helper Function ---
async function graphqlRequest(query, variables, token) {
  const headers = {
    'Content-Type': 'application/json',
  };
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const res = await fetch(ADMIN_API_URL, {
    method: 'POST',
    headers,
    body: JSON.stringify({ query, variables }),
  });

  const json = await res.json();
  if (json.errors) {
    throw new Error(json.errors.map(e => e.message).join('\n'));
  }
  return json.data;
}

// --- Main Test Function ---
async function main() {
  console.log('--- Starting Minimal Auth Test ---');
  let token;

  try {
    // 1. Authenticate and get token
    console.log(`Attempting to log in as '${USERNAME}'...`);
    const loginData = await graphqlRequest(LOGIN_MUTATION, { username: USERNAME, password: PASSWORD });
    
    const user = loginData.authenticate;
    if (!user || !user.channels) {
      throw new Error(`Login failed: ${user.message}`);
    }

    token = user.channels[0]?.token;
    if (!token) {
      throw new Error('Could not retrieve auth token.');
    }
    console.log('✅ Login successful. Token obtained.');

  } catch (err) {
    console.error('❌ FATAL: Could not complete login.', err.message);
    return;
  }

  try {
    // 2. Use the token to make an authorized query
    console.log('\nAttempting to fetch current user with the new token...');
    const meData = await graphqlRequest(GET_CURRENT_USER_QUERY, {}, token);

    console.log('✅ SUCCESS: Authorized query completed.');
    console.log('Current User:', JSON.stringify(meData.me, null, 2));

  } catch (err) {
    console.error('❌ FAILURE: Authorized query failed.');
    console.error('Error:', err.message);
  }

  console.log('\n--- Test Complete ---');
}

main();
